using System.ComponentModel.DataAnnotations;
using Workforce.Domain.Enums;

namespace Workforce.Domain.Entities
{
    /// <summary>
    /// Represents a user in the HireNow application
    /// Based on the ERD schema for the USERS table
    /// </summary>
    public class User
    {
        /// <summary>
        /// Unique identifier for the user
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// User's email address (unique)
        /// </summary>
        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Hashed password for authentication
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number (unique)
        /// </summary>
        [Phone]
        [MaxLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User's first name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's role in the application (JobSeeker or Employer)
        /// </summary>
        [Required]
        public UserRole Role { get; set; }

        /// <summary>
        /// URL to user's profile photo
        /// </summary>
        [MaxLength(500)]
        public string? ProfilePhotoUrl { get; set; }

        /// <summary>
        /// Indicates if the user's account has been verified
        /// </summary>
        public bool IsVerified { get; set; } = false;

        /// <summary>
        /// Indicates if the user's account is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Device token for push notifications
        /// </summary>
        [MaxLength(255)]
        public string? DeviceToken { get; set; }

        /// <summary>
        /// Firebase Cloud Messaging token for push notifications
        /// </summary>
        [MaxLength(255)]
        public string? FcmToken { get; set; }

        /// <summary>
        /// User preferences stored as JSON
        /// </summary>
        public string? Preferences { get; set; }

        /// <summary>
        /// App settings stored as JSON
        /// </summary>
        public string? AppSettings { get; set; }

        /// <summary>
        /// Timestamp of the user's last login
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// Timestamp when the user was last seen online
        /// </summary>
        public DateTime? LastSeenOnline { get; set; }

        /// <summary>
        /// Timestamp when the user account was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when the user account was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when the user account was soft deleted (null if not deleted)
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// Computed property for user's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Computed property to check if user is deleted
        /// </summary>
        public bool IsDeleted => DeletedAt.HasValue;
    }
}
