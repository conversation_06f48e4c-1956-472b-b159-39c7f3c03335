using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Workforce.Domain.Entities;
using Workforce.Infrastructure.Data;
using Workforce.Infrastructure.Repositories;
using Xunit;

namespace Workforce.Infrastructure.Tests.Repositories
{
    public class EmployeeRepositoryTests
    {
        private readonly DbContextOptions<ApplicationDbContext> _dbContextOptions;

        public EmployeeRepositoryTests()
        {
            // Use in-memory database for testing
            _dbContextOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: $"WorkforceTestDb_{Guid.NewGuid()}")
                .Options;
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnEmployee_WhenEmployeeExists()
        {
            // Arrange
            var employeeId = Guid.NewGuid();
            var departmentId = Guid.NewGuid();

            // Seed the database
            await using (var context = new ApplicationDbContext(_dbContextOptions))
            {
                var department = new Department
                {
                    Id = departmentId,
                    Name = "IT",
                    Description = "Information Technology"
                };
                
                var employee = new Employee
                {
                    Id = employeeId,
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    PhoneNumber = "************",
                    HireDate = new DateTime(2023, 1, 15),
                    Position = "Software Developer",
                    Salary = 75000m,
                    DepartmentId = departmentId
                };
                
                await context.Departments.AddAsync(department);
                await context.Employees.AddAsync(employee);
                await context.SaveChangesAsync();
            }

            // Act
            await using (var context = new ApplicationDbContext(_dbContextOptions))
            {
                var repository = new EmployeeRepository(context);
                var result = await repository.GetByIdAsync(employeeId);

                // Assert
                result.Should().NotBeNull();
                result.Id.Should().Be(employeeId);
                result.FirstName.Should().Be("John");
                result.LastName.Should().Be("Doe");
                result.Email.Should().Be("<EMAIL>");
                result.DepartmentId.Should().Be(departmentId);
            }
        }

        [Fact]
        public async Task GetEmployeesByDepartmentAsync_ShouldReturnEmployees_WhenDepartmentExists()
        {
            // Arrange
            var departmentId = Guid.NewGuid();

            // Seed the database
            await using (var context = new ApplicationDbContext(_dbContextOptions))
            {
                var department = new Department
                {
                    Id = departmentId,
                    Name = "IT",
                    Description = "Information Technology"
                };
                
                var employees = new List<Employee>
                {
                    new Employee
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "John",
                        LastName = "Doe",
                        Email = "<EMAIL>",
                        PhoneNumber = "************",
                        HireDate = new DateTime(2023, 1, 15),
                        Position = "Software Developer",
                        Salary = 75000m,
                        DepartmentId = departmentId
                    },
                    new Employee
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Jane",
                        LastName = "Smith",
                        Email = "<EMAIL>",
                        PhoneNumber = "************",
                        HireDate = new DateTime(2023, 2, 1),
                        Position = "QA Engineer",
                        Salary = 70000m,
                        DepartmentId = departmentId
                    }
                };
                
                await context.Departments.AddAsync(department);
                await context.Employees.AddRangeAsync(employees);
                await context.SaveChangesAsync();
            }

            // Act
            await using (var context = new ApplicationDbContext(_dbContextOptions))
            {
                var repository = new EmployeeRepository(context);
                var result = await repository.GetEmployeesByDepartmentAsync(departmentId);

                // Assert
                result.Should().NotBeNull();
                result.Should().HaveCount(2);
                result.Should().Contain(e => e.FirstName == "John" && e.LastName == "Doe");
                result.Should().Contain(e => e.FirstName == "Jane" && e.LastName == "Smith");
                result.All(e => e.DepartmentId == departmentId).Should().BeTrue();
            }
        }
    }
}
