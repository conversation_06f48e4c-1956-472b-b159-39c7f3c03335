using System;
using FluentAssertions;
using Workforce.Domain.Entities;
using Xunit;

namespace Workforce.Domain.Tests.Entities
{
    public class EmployeeTests
    {
        [Fact]
        public void Employee_ShouldHaveCorrectProperties()
        {
            // Arrange
            var id = Guid.NewGuid();
            var departmentId = Guid.NewGuid();
            var department = new Department { Id = departmentId, Name = "IT" };
            
            // Act
            var employee = new Employee
            {
                Id = id,
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                PhoneNumber = "************",
                HireDate = new DateTime(2023, 1, 15),
                Position = "Software Developer",
                Salary = 75000m,
                DepartmentId = departmentId,
                Department = department
            };
            
            // Assert
            employee.Id.Should().Be(id);
            employee.FirstName.Should().Be("John");
            employee.LastName.Should().Be("Doe");
            employee.Email.Should().Be("<EMAIL>");
            employee.PhoneNumber.Should().Be("************");
            employee.HireDate.Should().Be(new DateTime(2023, 1, 15));
            employee.Position.Should().Be("Software Developer");
            employee.Salary.Should().Be(75000m);
            employee.DepartmentId.Should().Be(departmentId);
            employee.Department.Should().BeSameAs(department);
        }
    }
}
