using AutoMapper;
using Workforce.Domain.Entities;
using Workforce.Shared.DTOs;
using Workforce.Application.Features.Users.Commands.RegisterUser;

namespace Workforce.Application.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserProfileDto>()
                .ForMember(dest => dest.Role, opt => opt.MapFrom(src => src.Role.ToString()));

            CreateMap<RegisterUserDto, RegisterUserCommand>();
        }
    }
}
