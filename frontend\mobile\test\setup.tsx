import React, { ReactNode } from 'react';
import { NavigationContainer, DefaultTheme } from '@react-navigation/native';
import { render as rtlRender, RenderOptions } from '@testing-library/react-native';

interface ProvidersProps {
  children: ReactNode;
}

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#2f95dc',
    background: '#fff',
    card: '#fff',
    text: '#000',
    border: '#ccc',
    notification: '#ff3b30',
  },
};

const AllTheProviders = ({ children }: ProvidersProps) => (
  <NavigationContainer theme={theme}>{children}</NavigationContainer>
);

const customRender = (ui: React.ReactElement, options?: Omit<RenderOptions, 'wrapper'>) =>
  rtlRender(ui, { wrapper: AllTheProviders, ...options });

// re-export everything
export * from '@testing-library/react-native';

// override render method
export { customRender as render };
