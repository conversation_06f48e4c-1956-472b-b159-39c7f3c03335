namespace Workforce.Shared.DTOs
{
    /// <summary>
    /// DTO for authenticated user response (includes token information)
    /// </summary>
    public class AuthenticatedUserDto
    {
        /// <summary>
        /// User's profile information
        /// </summary>
        public UserProfileDto User { get; set; } = new();

        /// <summary>
        /// JWT access token
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Refresh token for obtaining new access tokens
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// Token type (typically "Bearer")
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// Token expiration time in seconds
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// Timestamp when the token expires
        /// </summary>
        public DateTime ExpiresAt { get; set; }
    }
}
