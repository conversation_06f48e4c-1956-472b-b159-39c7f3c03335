namespace Workforce.Shared.DTOs
{
    /// <summary>
    /// DTO for user profile information (read model)
    /// </summary>
    public class UserProfileDto
    {
        /// <summary>
        /// User's unique identifier
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's full name
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User's role in the application
        /// </summary>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// URL to user's profile photo
        /// </summary>
        public string? ProfilePhotoUrl { get; set; }

        /// <summary>
        /// Indicates if the user's account has been verified
        /// </summary>
        public bool IsVerified { get; set; }

        /// <summary>
        /// Indicates if the user's account is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Timestamp when the user account was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Timestamp of the user's last login
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// Timestamp when the user was last seen online
        /// </summary>
        public DateTime? LastSeenOnline { get; set; }
    }
}
