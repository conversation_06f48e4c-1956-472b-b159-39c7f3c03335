using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Workforce.API.Controllers;
using Workforce.Application.Features.Departments.Commands.CreateDepartment;
using Workforce.Application.Features.Departments.Commands.DeleteDepartment;
using Workforce.Application.Features.Departments.Queries.GetDepartmentById;
using Workforce.Application.Features.Departments.Queries.GetDepartmentList;
using Workforce.Shared.DTOs;
using Xunit;

namespace Workforce.API.Tests.Controllers
{
    public class DepartmentsControllerTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly DepartmentsController _controller;

        public DepartmentsControllerTests()
        {
            _mockMediator = new Mock<IMediator>();
            _controller = new DepartmentsController(_mockMediator.Object);
        }

        [Fact]
        public async Task GetAll_ShouldReturnOkResult_WithListOfDepartments()
        {
            // Arrange
            var departments = new List<DepartmentDto>
            {
                new DepartmentDto
                {
                    Id = Guid.NewGuid(),
                    Name = "IT",
                    Description = "Information Technology"
                },
                new DepartmentDto
                {
                    Id = Guid.NewGuid(),
                    Name = "HR",
                    Description = "Human Resources"
                }
            };

            _mockMediator.Setup(m => m.Send(It.IsAny<GetDepartmentListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(departments);

            // Act
            var result = await _controller.GetAll();

            // Assert
            var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
            var returnedDepartments = okResult.Value.Should().BeAssignableTo<List<DepartmentDto>>().Subject;
            
            returnedDepartments.Should().HaveCount(2);
            returnedDepartments.Should().BeEquivalentTo(departments);
            
            _mockMediator.Verify(m => m.Send(It.IsAny<GetDepartmentListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetById_WithValidId_ShouldReturnOkResult_WithDepartment()
        {
            // Arrange
            var departmentId = Guid.NewGuid();
            var department = new DepartmentDto
            {
                Id = departmentId,
                Name = "IT",
                Description = "Information Technology"
            };

            _mockMediator.Setup(m => m.Send(It.Is<GetDepartmentByIdQuery>(q => q.Id == departmentId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(department);

            // Act
            var result = await _controller.GetById(departmentId);

            // Assert
            var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
            var returnedDepartment = okResult.Value.Should().BeAssignableTo<DepartmentDto>().Subject;
            
            returnedDepartment.Should().NotBeNull();
            returnedDepartment.Id.Should().Be(departmentId);
            returnedDepartment.Name.Should().Be("IT");
            returnedDepartment.Description.Should().Be("Information Technology");
            
            _mockMediator.Verify(m => m.Send(It.Is<GetDepartmentByIdQuery>(q => q.Id == departmentId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetById_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var departmentId = Guid.NewGuid();

            _mockMediator.Setup(m => m.Send(It.Is<GetDepartmentByIdQuery>(q => q.Id == departmentId), It.IsAny<CancellationToken>()))
                .ReturnsAsync((DepartmentDto)null);

            // Act
            var result = await _controller.GetById(departmentId);

            // Assert
            result.Result.Should().BeOfType<NotFoundResult>();
            
            _mockMediator.Verify(m => m.Send(It.Is<GetDepartmentByIdQuery>(q => q.Id == departmentId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Create_WithValidData_ShouldReturnCreatedAtAction()
        {
            // Arrange
            var createDto = new CreateDepartmentDto
            {
                Name = "IT",
                Description = "Information Technology"
            };

            var createdDepartment = new DepartmentDto
            {
                Id = Guid.NewGuid(),
                Name = "IT",
                Description = "Information Technology"
            };

            _mockMediator.Setup(m => m.Send(It.IsAny<CreateDepartmentCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(createdDepartment);

            // Act
            var result = await _controller.Create(createDto);

            // Assert
            var createdAtActionResult = result.Result.Should().BeOfType<CreatedAtActionResult>().Subject;
            createdAtActionResult.ActionName.Should().Be(nameof(DepartmentsController.GetById));
            createdAtActionResult.RouteValues["id"].Should().Be(createdDepartment.Id);
            
            var returnedDepartment = createdAtActionResult.Value.Should().BeAssignableTo<DepartmentDto>().Subject;
            returnedDepartment.Should().BeEquivalentTo(createdDepartment);
            
            _mockMediator.Verify(m => m.Send(It.IsAny<CreateDepartmentCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Delete_WithValidId_ShouldReturnNoContent()
        {
            // Arrange
            var departmentId = Guid.NewGuid();

            _mockMediator.Setup(m => m.Send(It.Is<DeleteDepartmentCommand>(c => c.Id == departmentId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Delete(departmentId);

            // Assert
            result.Should().BeOfType<NoContentResult>();
            
            _mockMediator.Verify(m => m.Send(It.Is<DeleteDepartmentCommand>(c => c.Id == departmentId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Delete_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var departmentId = Guid.NewGuid();

            _mockMediator.Setup(m => m.Send(It.Is<DeleteDepartmentCommand>(c => c.Id == departmentId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.Delete(departmentId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
            
            _mockMediator.Verify(m => m.Send(It.Is<DeleteDepartmentCommand>(c => c.Id == departmentId), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
