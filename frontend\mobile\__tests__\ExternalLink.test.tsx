import React from 'react';
import { fireEvent } from '@testing-library/react-native';
import * as WebBrowser from 'expo-web-browser';
import { render } from '../test/setup';
import { ExternalLink } from '../components/ExternalLink';

jest.mock('expo-web-browser', () => ({
  openBrowserAsync: jest.fn(),
}));

jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  select: jest.fn(),
}));

describe('ExternalLink', () => {
  it('renders correctly', () => {
    const testId = 'test-link';
    const { getByText } = render(
      <ExternalLink href="https://example.com" testID={testId}>
        Test Link
      </ExternalLink>,
    );

    expect(getByText('Test Link')).toBeTruthy();
  });

  it('opens the URL in web browser when pressed', () => {
    const url = 'https://example.com';
    const { getByText } = render(<ExternalLink href={url}>Test Link</ExternalLink>);

    fireEvent(getByText('Test Link'), 'onPress', {
      preventDefault: jest.fn(),
    });

    expect(WebBrowser.openBrowserAsync).toHaveBeenCalledWith(url);
  });
});
