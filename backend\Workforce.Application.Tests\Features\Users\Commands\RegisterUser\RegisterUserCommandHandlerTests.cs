using AutoMapper;
using FluentAssertions;
using Moq;
using Workforce.Application.Features.Users.Commands.RegisterUser;
using Workforce.Application.Mappings;
using Workforce.Application.Services;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;
using Workforce.Domain.Repositories;
using Xunit;

namespace Workforce.Application.Tests.Features.Users.Commands.RegisterUser
{
    public class RegisterUserCommandHandlerTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IPasswordHashingService> _mockPasswordHashingService;
        private readonly Mock<IJwtTokenService> _mockJwtTokenService;
        private readonly IMapper _mapper;
        private readonly RegisterUserCommandHandler _handler;

        public RegisterUserCommandHandlerTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockPasswordHashingService = new Mock<IPasswordHashingService>();
            _mockJwtTokenService = new Mock<IJwtTokenService>();

            // Setup AutoMapper
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile<MappingProfile>());
            _mapper = configuration.CreateMapper();

            // Setup UnitOfWork to return the mock user repository
            _mockUnitOfWork.Setup(x => x.Users).Returns(_mockUserRepository.Object);

            _handler = new RegisterUserCommandHandler(
                _mockUnitOfWork.Object,
                _mapper,
                _mockPasswordHashingService.Object,
                _mockJwtTokenService.Object);
        }

        [Fact]
        public async Task Handle_ValidCommand_ShouldCreateUserAndReturnAuthenticatedUserDto()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+1234567890",
                Role = "JobSeeker"
            };

            var hashedPassword = "hashed_password_123";
            var accessToken = "access_token_123";
            var refreshToken = "refresh_token_123";
            var expirationSeconds = 3600;

            _mockPasswordHashingService.Setup(x => x.HashPassword(command.Password))
                .Returns(hashedPassword);

            _mockJwtTokenService.Setup(x => x.GenerateAccessToken(It.IsAny<User>()))
                .Returns(accessToken);

            _mockJwtTokenService.Setup(x => x.GenerateRefreshToken())
                .Returns(refreshToken);

            _mockJwtTokenService.Setup(x => x.GetTokenExpirationSeconds())
                .Returns(expirationSeconds);

            _mockUnitOfWork.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.User.Should().NotBeNull();
            result.User.Email.Should().Be(command.Email.ToLowerInvariant());
            result.User.FirstName.Should().Be(command.FirstName);
            result.User.LastName.Should().Be(command.LastName);
            result.User.PhoneNumber.Should().Be(command.PhoneNumber);
            result.User.Role.Should().Be(command.Role);
            result.User.IsVerified.Should().BeFalse();
            result.User.IsActive.Should().BeTrue();

            result.AccessToken.Should().Be(accessToken);
            result.RefreshToken.Should().Be(refreshToken);
            result.TokenType.Should().Be("Bearer");
            result.ExpiresIn.Should().Be(expirationSeconds);

            // Verify interactions
            _mockPasswordHashingService.Verify(x => x.HashPassword(command.Password), Times.Once);
            _mockUserRepository.Verify(x => x.AddAsync(It.IsAny<User>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
            _mockJwtTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Once);
            _mockJwtTokenService.Verify(x => x.GenerateRefreshToken(), Times.Once);
        }

        [Fact]
        public async Task Handle_InvalidRole_ShouldThrowArgumentException()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                Role = "InvalidRole"
            };

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
        }

        [Theory]
        [InlineData("JobSeeker", UserRole.JobSeeker)]
        [InlineData("Employer", UserRole.Employer)]
        public async Task Handle_ValidRoles_ShouldSetCorrectUserRole(string roleString, UserRole expectedRole)
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                Role = roleString
            };

            var hashedPassword = "hashed_password_123";
            User capturedUser = null!;

            _mockPasswordHashingService.Setup(x => x.HashPassword(command.Password))
                .Returns(hashedPassword);

            _mockUserRepository.Setup(x => x.AddAsync(It.IsAny<User>()))
                .Callback<User>(user => capturedUser = user);

            _mockJwtTokenService.Setup(x => x.GenerateAccessToken(It.IsAny<User>()))
                .Returns("token");

            _mockJwtTokenService.Setup(x => x.GenerateRefreshToken())
                .Returns("refresh");

            _mockJwtTokenService.Setup(x => x.GetTokenExpirationSeconds())
                .Returns(3600);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            capturedUser.Should().NotBeNull();
            capturedUser.Role.Should().Be(expectedRole);
        }

        [Fact]
        public async Task Handle_EmptyPhoneNumber_ShouldSetPhoneNumberToNull()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "   ", // Whitespace only
                Role = "JobSeeker"
            };

            User capturedUser = null!;

            _mockPasswordHashingService.Setup(x => x.HashPassword(command.Password))
                .Returns("hashed_password");

            _mockUserRepository.Setup(x => x.AddAsync(It.IsAny<User>()))
                .Callback<User>(user => capturedUser = user);

            _mockJwtTokenService.Setup(x => x.GenerateAccessToken(It.IsAny<User>()))
                .Returns("token");

            _mockJwtTokenService.Setup(x => x.GenerateRefreshToken())
                .Returns("refresh");

            _mockJwtTokenService.Setup(x => x.GetTokenExpirationSeconds())
                .Returns(3600);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            capturedUser.Should().NotBeNull();
            capturedUser.PhoneNumber.Should().BeNull();
        }
    }
}
