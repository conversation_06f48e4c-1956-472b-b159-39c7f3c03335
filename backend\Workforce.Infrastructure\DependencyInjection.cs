using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Workforce.Domain.Repositories;
using Workforce.Infrastructure.Data;
using Workforce.Infrastructure.Repositories;
using Workforce.Infrastructure.Settings;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Workforce.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure and register DatabaseSettings
            var databaseSettings = new DatabaseSettings();
            configuration.GetSection("DatabaseSettings").Bind(databaseSettings);
            services.Configure<DatabaseSettings>(options =>
            {
                options.Host = databaseSettings.Host;
                options.Port = databaseSettings.Port;
                options.Database = databaseSettings.Database;
                options.Username = databaseSettings.Username;
                options.Password = databaseSettings.Password;
            });
            services.AddSingleton(databaseSettings);

            // Determine environment
            var isDevelopment = configuration.GetValue<bool>("IsDevelopment") ||
                                Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";

            // Register DbContext with environment-specific options
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseNpgsql(databaseSettings.GetConnectionString(),
                    b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName));

                // Enable detailed errors and sensitive data logging in development
                if (isDevelopment)
                {
                    options.EnableSensitiveDataLogging();
                    options.EnableDetailedErrors();
                }
            });

            // Register repositories
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<IDepartmentRepository, DepartmentRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Register migration service for development environments
            if (isDevelopment)
            {
                services.AddHostedService<DatabaseMigrationService>();
            }

            return services;
        }
    }

    /// <summary>
    /// Hosted service that runs at application startup to ensure the database schema matches the current model
    /// </summary>
    public class DatabaseMigrationService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DatabaseMigrationService> _logger;

        public DatabaseMigrationService(
            IServiceProvider serviceProvider,
            ILogger<DatabaseMigrationService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Ensuring database schema matches current model");

            // Create a new scope to resolve scoped services
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            try
            {
                await dbContext.Database.MigrateAsync(cancellationToken);
                _logger.LogInformation("Database schema is up to date");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while ensuring database schema is up to date");
                // We don't rethrow because we don't want to prevent the application from starting
            }
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
