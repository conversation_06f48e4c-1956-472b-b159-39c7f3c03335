using Workforce.Domain.Enums;
using Workforce.Domain.Entities;

namespace Workforce.Domain.Repositories
{
    /// <summary>
    /// Repository interface for User entity operations
    /// </summary>
    public interface IUserRepository : IRepository<User>
    {
        /// <summary>
        /// Gets a user by their email address
        /// </summary>
        /// <param name="email">The email address to search for</param>
        /// <returns>The user if found, null otherwise</returns>
        Task<User?> GetByEmailAsync(string email);

        /// <summary>
        /// Gets a user by their phone number
        /// </summary>
        /// <param name="phoneNumber">The phone number to search for</param>
        /// <returns>The user if found, null otherwise</returns>
        Task<User?> GetByPhoneNumberAsync(string phoneNumber);

        /// <summary>
        /// Checks if an email address is already in use
        /// </summary>
        /// <param name="email">The email address to check</param>
        /// <param name="excludeUserId">Optional user ID to exclude from the check (for updates)</param>
        /// <returns>True if email exists, false otherwise</returns>
        Task<bool> EmailExistsAsync(string email, Guid? excludeUserId = null);

        /// <summary>
        /// Checks if a phone number is already in use
        /// </summary>
        /// <param name="phoneNumber">The phone number to check</param>
        /// <param name="excludeUserId">Optional user ID to exclude from the check (for updates)</param>
        /// <returns>True if phone number exists, false otherwise</returns>
        Task<bool> PhoneNumberExistsAsync(string phoneNumber, Guid? excludeUserId = null);

        /// <summary>
        /// Gets users by their role
        /// </summary>
        /// <param name="role">The user role to filter by</param>
        /// <returns>Collection of users with the specified role</returns>
        Task<IEnumerable<User>> GetByRoleAsync(UserRole role);

        /// <summary>
        /// Gets active users (not soft deleted)
        /// </summary>
        /// <returns>Collection of active users</returns>
        Task<IEnumerable<User>> GetActiveUsersAsync();

        /// <summary>
        /// Updates the user's last login timestamp
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="loginTime">The login timestamp</param>
        Task UpdateLastLoginAsync(Guid userId, DateTime loginTime);

        /// <summary>
        /// Updates the user's last seen online timestamp
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="lastSeenTime">The last seen timestamp</param>
        Task UpdateLastSeenOnlineAsync(Guid userId, DateTime lastSeenTime);
    }
}
