using System;
using System.Threading.Tasks;
using Workforce.Domain.Repositories;
using Workforce.Infrastructure.Data;

namespace Workforce.Infrastructure.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _context;
        private IEmployeeRepository? _employeeRepository;
        private IDepartmentRepository? _departmentRepository;
        private IUserRepository? _userRepository;
        private bool _disposed = false;

        public UnitOfWork(ApplicationDbContext context)
        {
            _context = context;
        }

        public IEmployeeRepository Employees => _employeeRepository ??= new EmployeeRepository(_context);

        public IDepartmentRepository Departments => _departmentRepository ??= new DepartmentRepository(_context);

        public IUserRepository Users => _userRepository ??= new UserRepository(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
