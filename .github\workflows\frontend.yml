name: Frontend CI/CD

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - '.github/workflows/frontend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - '.github/workflows/frontend.yml'

jobs:
  build-and-test:
    name: Build and Test
    runs-on: [self-hosted]
    steps:   
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
          
      - name: Install Android SDK
        uses: android-actions/setup-android@v3
        
      - name: Install Ninja
        run: sudo apt-get update && sudo apt-get install -y ninja-build

      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/mobile/package-lock.json
          
      - name: Install dependencies
        working-directory: frontend/mobile
        run: npm ci
        
      - name: Run linting
        working-directory: frontend/mobile
        run: npm run lint
        
      - name: Run tests
        working-directory: frontend/mobile
        run: npm run test
        
      - name: Build project
        working-directory: frontend/mobile
        run: npx expo prebuild
        
      - name: Build APK
        # if: github.ref == 'refs/heads/main'
        working-directory: frontend/mobile/android
        run: ./gradlew assembleRelease
        
      - name: Upload APK
        #if: github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v4
        with:
          name: app-release
          path: frontend/mobile/android/app/build/outputs/apk/release/app-release.apk

  deploy:
    name: Deploy
    needs: build-and-test
    # if: github.ref == 'refs/heads/main'
    runs-on: [self-hosted]
    steps:
      - uses: actions/checkout@v4
      
      - name: Download APK
        uses: actions/download-artifact@v4
        with:
          name: app-release
          path: frontend/mobile/android/app/build/outputs/apk/release
          
      # Add your frontend deployment steps here
      # For example, uploading to Google Play Store or other distribution platforms
      - name: Deploy Frontend
        run: |
          echo "Add your frontend deployment steps here"
          # Example: Upload to Google Play Store
          # - name: Upload to Google Play
          #   uses: r0adkll/upload-google-play@v1
          #   with:
          #     serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          #     packageName: com.your.package
          #     releaseFiles: frontend/mobile/android/app/build/outputs/apk/release/app-release.apk
          #     track: production 