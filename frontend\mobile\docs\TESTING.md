# React Native Testing Guide

This document provides information about testing our React Native Expo application.

## Testing Stack

Our testing stack includes:

- **Jest**: Test runner and assertion library
- **jest-expo**: Preset for testing Expo applications
- **@testing-library/react-native**: Testing utilities for React Native (including snapshot testing)
- **@testing-library/jest-native**: Custom Jest matchers for testing React Native components

## Running Tests

We use a Makefile to simplify test execution. Make sure you have `make` installed on your system.

### Common Test Commands

```bash
# Run all tests
make test

# Run tests in watch mode (continuous)
make test-watch

# Run tests with coverage report
make test-coverage

# Run only StyledText component tests
make test-styled

# Install testing dependencies
make setup-tests

# Run linting
make lint
```

### Using npm Directly

You can also run tests directly with npm:

```bash
# Run all tests
npm test

# Run specific tests (by name pattern)
npm test -- -t "StyledText"

# Run tests in a specific file
npm test -- __tests__/StyledText.test.tsx

# Run tests with coverage
npm run test:coverage

# Update snapshots
npm test -- -u
```

## Test File Structure

- All test files are located in the `__tests__` directory at the root level
- Test files should follow the naming convention: `[ComponentName].test.tsx`
- Snapshot files are stored in `__tests__/__snapshots__/`
- Mock files are stored in `__mocks__/` directory

## Writing Tests

### Basic Component Test Example

Here's a basic example of how to test a component:

```typescript
import React from 'react';
import { render } from '@testing-library/react-native';
import { MonoText } from '../components/StyledText';

describe('MonoText component', () => {
  it('renders correctly with proper styling', () => {
    const testMessage = 'Hello World';
    const { getByText } = render(<MonoText>{testMessage}</MonoText>);

    const textElement = getByText(testMessage);

    // Check if the component renders the correct text
    expect(textElement).toBeTruthy();

    // Check if the style contains the SpaceMono font family
    // Handle nested style arrays properly
    const flattenedStyles = textElement.props.style.flat();
    expect(flattenedStyles).toContainEqual(
      expect.objectContaining({
        fontFamily: 'SpaceMono',
      })
    );
  });

  // Snapshot testing with @testing-library/react-native
  it('matches snapshot', () => {
    const { toJSON } = render(<MonoText>Snapshot test!</MonoText>);
    expect(toJSON()).toMatchSnapshot();
  });
});
```

### Testing User Interactions

```typescript
import { render, fireEvent } from '@testing-library/react-native';

it('handles button press', () => {
  const onPressMock = jest.fn();
  const { getByText } = render(<Button onPress={onPressMock} title="Press Me" />);

  fireEvent.press(getByText('Press Me'));

  expect(onPressMock).toHaveBeenCalled();
});
```

## Mocking Dependencies

When testing components that use external dependencies, you should mock those dependencies. There are two ways to do this:

### 1. In-line Mock

```typescript
jest.mock('expo-font', () => ({
  loadAsync: jest.fn().mockResolvedValue(true),
}));
```

### 2. Mock Files

Create a `__mocks__` directory and add mock files for each module you want to mock. For example, `__mocks__/expo-router.js`:

```javascript
// Mock for expo-router
const React = require('react');
const { Text, TouchableOpacity } = require('react-native');

// Basic Link component mock
const Link = props => {
  return {
    type: 'TouchableOpacity',
    props: {
      ...props,
      testID: 'mock-link',
    },
  };
};

module.exports = {
  Link,
  // Other exports...
};
```

### 3. Testing Complex Components with Mocks

For complex component tests where you need to test the behavior of one component that depends on another component, you can override the mock in your test file:

```typescript
// Create a mock component that will receive props from target component
const mockComponent = jest.fn();

// Override Link to capture props
require('expo-router').Link = (props) => {
  mockComponent(props);
  return null;
};

// Render the component
render(<ExternalLink href="https://example.com">Test Link</ExternalLink>);

// Extract and test the onPress handler
const onPress = mockComponent.mock.calls[0][0].onPress;
const mockEvent = { preventDefault: jest.fn() };
onPress(mockEvent);

// Verify behavior
expect(mockEvent.preventDefault).toHaveBeenCalled();
```

## Testing Platform-Specific Code

To test platform-specific behavior, mock the Platform module:

```typescript
import { Platform } from 'react-native';

// Mock to change platform during test
const mockPlatform = platform => {
  Platform.OS = platform;
};

it('behaves differently on iOS', () => {
  mockPlatform('ios');
  // Test iOS-specific behavior
});

it('behaves differently on web', () => {
  mockPlatform('web');
  // Test web-specific behavior
});
```

## Best Practices

1. **Use @testing-library/react-native for all tests**, including snapshots
2. **Test from a user's perspective** rather than implementation details
3. **Keep all tests in .tsx files** for TypeScript type safety
4. **Organize tests consistently** in the `__tests__` directory
5. **Mock external dependencies** to isolate component behavior
6. **Test platform-specific behavior** by mocking the Platform module
7. **Prefer using render and fireEvent from @testing-library/react-native** over the older react-test-renderer

## Troubleshooting

If you encounter issues with tests:

1. Make sure all dependencies are installed: `make setup-tests`
2. Check Jest configuration in `jest.config.js`
3. Ensure that you've properly mocked any native modules
4. For complex components, consider incrementally building tests, starting with the simplest functionality
5. Use `console.log` statements in your tests to debug issues (they will appear in the test output)
6. If you're getting TypeScript errors for imported modules, check if you need to install type definitions
