using Workforce.Domain.Entities;

namespace Workforce.Application.Services
{
    /// <summary>
    /// Service interface for JWT token operations
    /// </summary>
    public interface IJwtTokenService
    {
        /// <summary>
        /// Generates a JWT access token for the user
        /// </summary>
        /// <param name="user">The user to generate the token for</param>
        /// <returns>The JWT token</returns>
        string GenerateAccessToken(User user);

        /// <summary>
        /// Generates a refresh token
        /// </summary>
        /// <returns>The refresh token</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// Gets the token expiration time in seconds
        /// </summary>
        /// <returns>Token expiration time in seconds</returns>
        int GetTokenExpirationSeconds();

        /// <summary>
        /// Validates a JWT token
        /// </summary>
        /// <param name="token">The token to validate</param>
        /// <returns>True if the token is valid, false otherwise</returns>
        bool ValidateToken(string token);

        /// <summary>
        /// Gets the user ID from a JWT token
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <returns>The user ID if valid, null otherwise</returns>
        Guid? GetUserIdFromToken(string token);
    }
}
