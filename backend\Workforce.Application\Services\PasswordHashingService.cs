using Microsoft.AspNetCore.Identity;

namespace Workforce.Application.Services
{
    /// <summary>
    /// Service for password hashing operations using ASP.NET Core Identity PasswordHasher
    /// </summary>
    public class PasswordHashingService : IPasswordHashingService
    {
        private readonly PasswordHasher<object> _passwordHasher;

        public PasswordHashingService()
        {
            _passwordHasher = new PasswordHasher<object>();
        }

        /// <summary>
        /// Hashes a password using a secure hashing algorithm
        /// </summary>
        /// <param name="password">The plain text password to hash</param>
        /// <returns>The hashed password</returns>
        public string HashPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            return _passwordHasher.HashPassword(null!, password);
        }

        /// <summary>
        /// Verifies a password against a hash
        /// </summary>
        /// <param name="hashedPassword">The hashed password</param>
        /// <param name="providedPassword">The plain text password to verify</param>
        /// <returns>True if the password matches the hash, false otherwise</returns>
        public bool VerifyPassword(string hashedPassword, string providedPassword)
        {
            if (string.IsNullOrWhiteSpace(hashedPassword))
                throw new ArgumentException("Hashed password cannot be null or empty", nameof(hashedPassword));

            if (string.IsNullOrWhiteSpace(providedPassword))
                throw new ArgumentException("Provided password cannot be null or empty", nameof(providedPassword));

            var result = _passwordHasher.VerifyHashedPassword(null!, hashedPassword, providedPassword);
            return result == PasswordVerificationResult.Success || result == PasswordVerificationResult.SuccessRehashNeeded;
        }
    }
}
