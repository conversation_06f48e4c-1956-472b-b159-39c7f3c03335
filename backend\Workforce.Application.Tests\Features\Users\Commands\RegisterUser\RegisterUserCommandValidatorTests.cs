using FluentAssertions;
using Moq;
using Workforce.Application.Features.Users.Commands.RegisterUser;
using Workforce.Domain.Repositories;
using Xunit;

namespace Workforce.Application.Tests.Features.Users.Commands.RegisterUser
{
    public class RegisterUserCommandValidatorTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly RegisterUserCommandValidator _validator;

        public RegisterUserCommandValidatorTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockUnitOfWork.Setup(x => x.Users).Returns(_mockUserRepository.Object);
            _validator = new RegisterUserCommandValidator(_mockUnitOfWork.Object);
        }

        [Fact]
        public async Task Validate_ValidCommand_ShouldNotHaveValidationErrors()
        {
            // Arrange
            var command = new RegisterUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+1234567890",
                Role = "JobSeeker"
            };

            _mockUserRepository.Setup(x => x.EmailExistsAsync(It.IsAny<string>(), It.IsAny<Guid?>()))
                .ReturnsAsync(false);

            _mockUserRepository.Setup(x => x.PhoneNumberExistsAsync(It.IsAny<string>(), It.IsAny<Guid?>()))
                .ReturnsAsync(false);

            // Act
            var result = await _validator.ValidateAsync(command);

            // Assert
            result.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData(null)]
        public async Task Validate_EmptyEmail_ShouldHaveValidationError(string? email)
        {
            // Arrange
            var command = new RegisterUserCommand { Email = email };

            // Act
            var result = await _validator.ValidateAsync(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(e => e.PropertyName == nameof(command.Email) && e.ErrorMessage == "Email is required");
        }

        [Theory]
        [InlineData("invalid-email")]
        [InlineData("@example.com")]
        [InlineData("test@")]
        [InlineData("test.example.com")]
        public async Task Validate_InvalidEmailFormat_ShouldHaveValidationError(string email)
        {
            // Arrange
            var command = new RegisterUserCommand { Email = email };

            // Act
            var result = await _validator.ValidateAsync(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(e => e.PropertyName == nameof(command.Email) && e.ErrorMessage == "Invalid email format");
        }

        [Fact]
        public async Task Validate_EmailAlreadyExists_ShouldHaveValidationError()
        {
            // Arrange
            var command = new RegisterUserCommand { Email = "<EMAIL>" };

            _mockUserRepository.Setup(x => x.EmailExistsAsync(It.IsAny<string>(), It.IsAny<Guid?>()))
                .ReturnsAsync(true);

            // Act
            var result = await _validator.ValidateAsync(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(e => e.PropertyName == nameof(command.Email) && e.ErrorMessage == "Email is already registered");
        }

        [Fact]
        public async Task Validate_WeakPassword_ShouldHaveValidationError()
        {
            // Arrange
            var command = new RegisterUserCommand { Password = "password123" }; // No uppercase or special character

            // Act
            var result = await _validator.ValidateAsync(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(e => e.PropertyName == nameof(command.Password));
        }

        [Fact]
        public async Task Validate_InvalidRole_ShouldHaveValidationError()
        {
            // Arrange
            var command = new RegisterUserCommand { Role = "InvalidRole" };

            // Act
            var result = await _validator.ValidateAsync(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(e => e.PropertyName == nameof(command.Role) && e.ErrorMessage == "Role must be either 'JobSeeker' or 'Employer'");
        }
    }
}
