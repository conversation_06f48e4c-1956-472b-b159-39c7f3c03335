using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FluentAssertions;
using Moq;
using Workforce.Application.Features.Employees.Commands.CreateEmployee;
using Workforce.Application.Mappings;
using Workforce.Domain.Entities;
using Workforce.Domain.Repositories;
using Workforce.Shared.DTOs;
using Xunit;

namespace Workforce.Application.Tests.Features.Employees.Commands
{
    public class CreateEmployeeCommandHandlerTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IEmployeeRepository> _mockEmployeeRepository;
        private readonly IMapper _mapper;

        public CreateEmployeeCommandHandlerTests()
        {
            // Setup repositories
            _mockEmployeeRepository = new Mock<IEmployeeRepository>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUnitOfWork.Setup(uow => uow.Employees).Returns(_mockEmployeeRepository.Object);
            
            // Setup AutoMapper
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MappingProfile>();
            });
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public async Task Handle_ShouldCreateEmployee_AndReturnEmployeeDto()
        {
            // Arrange
            var command = new CreateEmployeeCommand
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                PhoneNumber = "************",
                HireDate = new DateTime(2023, 1, 15),
                Position = "Software Developer",
                Salary = 75000m,
                DepartmentId = Guid.NewGuid()
            };

            Employee savedEmployee = null;
            
            _mockEmployeeRepository.Setup(repo => repo.AddAsync(It.IsAny<Employee>()))
                .Callback<Employee>(employee => savedEmployee = employee)
                .Returns(Task.CompletedTask);
                
            _mockUnitOfWork.Setup(uow => uow.SaveChangesAsync())
                .ReturnsAsync(1);
                
            var handler = new CreateEmployeeCommandHandler(_mockUnitOfWork.Object, _mapper);

            // Act
            var result = await handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeOfType<EmployeeDto>();
            result.FirstName.Should().Be(command.FirstName);
            result.LastName.Should().Be(command.LastName);
            result.Email.Should().Be(command.Email);
            result.PhoneNumber.Should().Be(command.PhoneNumber);
            result.HireDate.Should().Be(command.HireDate);
            result.Position.Should().Be(command.Position);
            result.Salary.Should().Be(command.Salary);
            result.DepartmentId.Should().Be(command.DepartmentId);
            
            savedEmployee.Should().NotBeNull();
            savedEmployee.Id.Should().NotBeEmpty();
            savedEmployee.FirstName.Should().Be(command.FirstName);
            
            _mockEmployeeRepository.Verify(repo => repo.AddAsync(It.IsAny<Employee>()), Times.Once);
            _mockUnitOfWork.Verify(uow => uow.SaveChangesAsync(), Times.Once);
        }
    }
}
