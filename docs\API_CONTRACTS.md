# HireNow API Contracts

## Overview

This document defines the REST API endpoints for the HireNow platform, supporting a mobile application that connects job seekers with employers for flexible, part-time work opportunities.

## Base URL

```
https://www.hirenow.com/api/v1
```

## Authentication

All API requests require authentication using JWT tokens.

### Headers

```
Authorization: Bearer {jwt_token}
```

## Response Format

All responses follow a standard format:

```json
{
  "success": true,
  "data": { ... },
  "message": "Success message",
  "errors": []
}
```

For errors:

```json
{
  "success": false,
  "data": null,
  "message": "Error summary",
  "errors": ["Detailed error message"]
}
```

## API Endpoints

### Authentication

#### POST /auth/register

Register a new user account.

Request:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "phone_number": "**********",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON>e",
    "token": "jwt_token"
  },
  "message": "Registration successful"
}
```

#### POST /auth/login

Authenticate a user.

Request:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "email": "<EMAIL>",
    "role": "job_seeker/employer",
    "token": "jwt_token",
    "profile_complete": true
  },
  "message": "Login successful"
}
```

#### POST /auth/social-login

Authenticate using a social provider.

Request:
```json
{
  "provider": "google",
  "token": "provider_token"
}
```

Response: Same as regular login

#### POST /auth/forgot-password

Request a password reset.

Request:
```json
{
  "email": "<EMAIL>"
}
```

Response:
```json
{
  "success": true,
  "message": "Reset instructions sent to email"
}
```

### User Profile

#### POST /users/role

Set user role (job seeker or employer).

Request:
```json
{
  "role": "job_seeker"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "role": "job_seeker",
    "profile_id": "uuid" 
  },
  "message": "Role set successfully"
}
```

#### GET /users/profile

Get current user profile.

Response:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "email": "<EMAIL>",
    "role": "job_seeker",
    "profile": {
      // Role-specific profile data
    }
  }
}
```

#### PUT /users/profile

Update user profile (job seeker).

Request:
```json
{
  "full_name": "John Doe",
  "birth_date": "1990-01-01",
  "bio": "Reliable worker with experience...",
  "profile_photo": "base64_image_data",
  "address": "123 Main St, San Francisco, CA",
  "location_coordinates": {
    "latitude": 37.7749,
    "longitude": -122.4194
  },
  "skills": ["Customer Service", "Delivery", "Warehouse"],
  "education": [...],
  "certifications": [...],
  "experience": [...],
  "availability": {
    "monday": true,
    "tuesday": true,
    "wednesday": false,
    "thursday": true,
    "friday": true,
    "saturday": true,
    "sunday": false
  }
}
```

Response:
```json
{
  "success": true,
  "data": {
    "profile_id": "uuid",
    "profile_complete": true
  },
  "message": "Profile updated successfully"
}
```

#### PUT /employers/profile

Update employer profile.

Request:
```json
{
  "company_name": "Bay Area Events",
  "company_logo": "base64_image_data",
  "industry": "Hospitality & Events",
  "company_size": "11-50",
  "website_url": "https://bayareaevents.com",
  "business_phone": "**********",
  "address": "123 Main St, San Francisco, CA",
  "location_coordinates": {
    "latitude": 37.7749,
    "longitude": -122.4194
  },
  "company_description": "Event planning company..."
}
```

Response:
```json
{
  "success": true,
  "data": {
    "profile_id": "uuid",
    "profile_complete": true
  },
  "message": "Profile updated successfully"
}
```

### Job Management

#### POST /jobs

Create a new job listing.

Request:
```json
{
  "title": "Event Staff",
  "category_id": "uuid",
  "description": "We are looking for reliable event staff...",
  "pay_rate": 22.00,
  "payment_type": "hourly",
  "duration_hours": 6,
  "start_datetime": "2023-07-28T17:00:00Z",
  "end_datetime": "2023-07-28T23:00:00Z",
  "address": "123 Main St, San Francisco, CA",
  "location": {
    "latitude": 37.7749,
    "longitude": -122.4194
  },
  "positions_available": 3,
  "required_skills": ["Customer Service", "Hospitality", "Communication"],
  "experience_level": "entry",
  "education_level": "high_school",
  "physical_requirements": ["stand_extended_periods", "lift_25_pounds"],
  "dress_code": "Black slacks, white button-up shirt...",
  "featured_listing": true,
  "application_questions": []
}
```

Response:
```json
{
  "success": true,
  "data": {
    "job_id": "uuid",
    "status": "active",
    "expiry_date": "2023-07-27T17:00:00Z"
  },
  "message": "Job created successfully"
}
```

#### GET /jobs

Get job listings with filters.

Query Parameters:
- status: active,expired,draft (comma-separated)
- category_id: uuid
- location_lat: float
- location_lng: float
- radius: int (in miles)
- pay_min: float
- pay_max: float
- date_start: ISO date
- date_end: ISO date
- search: string
- sort: recent,pay_high,pay_low,distance
- page: int
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "job_id": "uuid",
        "title": "Event Staff",
        "employer": {
          "company_name": "Bay Area Events",
          "logo_url": "https://...",
          "avg_rating": 4.0,
          "review_count": 23
        },
        "category": "Hospitality & Events",
        "pay_rate": 22.00,
        "payment_type": "hourly",
        "duration_hours": 6,
        "start_datetime": "2023-07-28T17:00:00Z",
        "address": "San Francisco, CA",
        "distance": 1.2,
        "posted_at": "2023-07-23T14:30:00Z",
        "application_count": 8,
        "status": "active"
      }
    ],
    "pagination": {
      "total": 45,
      "page": 1,
      "limit": 10,
      "total_pages": 5
    }
  }
}
```

#### GET /jobs/{job_id}

Get detailed job information.

Response:
```json
{
  "success": true,
  "data": {
    "job_id": "uuid",
    "title": "Event Staff",
    "category_id": "uuid",
    "category_name": "Hospitality & Events",
    "description": "We are looking for reliable event staff...",
    "pay_rate": 22.00,
    "payment_type": "hourly",
    "duration_hours": 6,
    "start_datetime": "2023-07-28T17:00:00Z",
    "end_datetime": "2023-07-28T23:00:00Z",
    "address": "123 Main St, San Francisco, CA",
    "location": {
      "latitude": 37.7749,
      "longitude": -122.4194
    },
    "distance": 1.2,
    "positions_available": 3,
    "positions_filled": 1,
    "required_skills": ["Customer Service", "Hospitality", "Communication"],
    "experience_level": "entry",
    "education_level": "high_school",
    "physical_requirements": ["stand_extended_periods", "lift_25_pounds"],
    "dress_code": "Black slacks, white button-up shirt...",
    "application_count": 8,
    "status": "active",
    "posted_at": "2023-07-23T14:30:00Z",
    "employer": {
      "employer_id": "uuid",
      "company_name": "Bay Area Events",
      "logo_url": "https://...",
      "avg_rating": 4.0,
      "review_count": 23
    },
    "has_applied": false,
    "is_saved": true
  }
}
```

#### PUT /jobs/{job_id}

Update a job listing.

Request: Same as POST with updated fields

Response:
```json
{
  "success": true,
  "data": {
    "job_id": "uuid",
    "status": "active" 
  },
  "message": "Job updated successfully"
}
```

#### DELETE /jobs/{job_id}

Delete a job listing.

Response:
```json
{
  "success": true,
  "message": "Job deleted successfully"
}
```

#### POST /jobs/{job_id}/save

Save a job for later.

Response:
```json
{
  "success": true,
  "data": {
    "saved_id": "uuid"
  },
  "message": "Job saved successfully"
}
```

#### DELETE /jobs/{job_id}/save

Remove a saved job.

Response:
```json
{
  "success": true,
  "message": "Job removed from saved list"
}
```

### Applications

#### POST /jobs/{job_id}/apply

Apply for a job.

Request:
```json
{
  "cover_note": "I'm interested in this position because...",
  "answers": [
    {
      "question_id": "uuid",
      "answer": "Yes, I have experience..."
    }
  ]
}
```

Response:
```json
{
  "success": true,
  "data": {
    "application_id": "uuid",
    "status": "submitted",
    "applied_at": "2023-07-24T15:45:00Z"
  },
  "message": "Application submitted successfully"
}
```

#### GET /applications

Get user's applications.

Query Parameters:
- status: all,pending,accepted,declined,completed (default: all)
- page: int
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "applications": [
      {
        "application_id": "uuid",
        "job_id": "uuid",
        "status": "pending",
        "applied_at": "2023-07-22T10:30:00Z",
        "job": {
          "title": "Event Staff",
          "employer": {
            "company_name": "Bay Area Events",
            "logo_url": "https://..."
          },
          "pay_rate": 22.00,
          "payment_type": "hourly",
          "start_datetime": "2023-07-28T17:00:00Z",
          "duration_hours": 6
        }
      }
    ],
    "pagination": {
      "total": 12,
      "page": 1,
      "limit": 10,
      "total_pages": 2
    }
  }
}
```

#### GET /applications/{application_id}

Get application details.

Response:
```json
{
  "success": true,
  "data": {
    "application_id": "uuid",
    "job_id": "uuid",
    "status": "accepted",
    "cover_note": "I'm interested in this position because...",
    "answers": [...],
    "applied_at": "2023-07-22T10:30:00Z",
    "employer_viewed": true,
    "employer_viewed_at": "2023-07-22T14:20:00Z",
    "scheduled_time": "2023-07-28T17:00:00Z",
    "job": {
      "title": "Event Staff",
      "description": "We are looking for reliable event staff...",
      "pay_rate": 22.00,
      "payment_type": "hourly",
      "start_datetime": "2023-07-28T17:00:00Z",
      "end_datetime": "2023-07-28T23:00:00Z",
      "address": "123 Main St, San Francisco, CA",
      "employer": {
        "company_name": "Bay Area Events",
        "logo_url": "https://..."
      }
    }
  }
}
```

#### PUT /applications/{application_id}/status

Update application status (employer only).

Request:
```json
{
  "status": "accepted",
  "rejection_reason": null
}
```

Response:
```json
{
  "success": true,
  "data": {
    "application_id": "uuid",
    "status": "accepted"
  },
  "message": "Application status updated"
}
```

#### PUT /applications/{application_id}/check-in

Check in for a job (job seeker only).

Request:
```json
{
  "location": {
    "latitude": 37.7749,
    "longitude": -122.4194
  }
}
```

Response:
```json
{
  "success": true,
  "data": {
    "application_id": "uuid",
    "check_in_time": "2023-07-28T17:05:00Z",
    "status": "in_progress"
  },
  "message": "Successfully checked in"
}
```

#### PUT /applications/{application_id}/check-out

Check out from a job (job seeker only).

Request:
```json
{
  "location": {
    "latitude": 37.7749,
    "longitude": -122.4194
  }
}
```

Response:
```json
{
  "success": true,
  "data": {
    "application_id": "uuid",
    "check_out_time": "2023-07-28T23:10:00Z",
    "hours_worked": 6.08,
    "status": "completed"
  },
  "message": "Successfully checked out"
}
```

### Payments

#### POST /applications/{application_id}/payment

Process payment for completed job (employer only).

Request:
```json
{
  "hours_worked": 6.0,
  "pay_rate": 22.00,
  "tip_amount": 10.00,
  "payment_method_id": "uuid"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "payment_id": "uuid",
    "application_id": "uuid",
    "job_id": "uuid",
    "amount": 132.00,
    "tip_amount": 10.00,
    "platform_fee": 13.20,
    "total_amount": 155.20,
    "status": "completed",
    "paid_at": "2023-07-29T10:15:00Z"
  },
  "message": "Payment processed successfully"
}
```

#### GET /payments

Get payment history.

Query Parameters:
- type: received,sent (required)
- date_start: ISO date
- date_end: ISO date
- page: int
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "payment_id": "uuid",
        "job_id": "uuid",
        "application_id": "uuid",
        "job_title": "Event Staff",
        "counterparty_name": "Bay Area Events",
        "amount": 132.00,
        "tip_amount": 10.00,
        "total_amount": 142.00,
        "status": "completed",
        "paid_at": "2023-07-15T14:30:00Z"
      }
    ],
    "summary": {
      "total_amount": 324.00,
      "total_count": 3,
      "period": "last_30_days"
    },
    "pagination": {
      "total": 3,
      "page": 1,
      "limit": 10,
      "total_pages": 1
    }
  }
}
```

### Ratings & Reviews

#### POST /applications/{application_id}/rate

Rate and review after job completion.

Request:
```json
{
  "rating_value": 4.0,
  "review_text": "John was extremely reliable and completed all tasks efficiently...",
  "category_ratings": {
    "punctuality": 5,
    "communication": 4,
    "work_quality": 3
  },
  "tip_amount": 10.00
}
```

Response:
```json
{
  "success": true,
  "data": {
    "rating_id": "uuid",
    "application_id": "uuid"
  },
  "message": "Rating submitted successfully"
}
```

#### GET /users/{user_id}/ratings

Get ratings for a user.

Query Parameters:
- page: int
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "average_rating": 4.0,
    "total_reviews": 26,
    "category_averages": {
      "punctuality": 4.2,
      "communication": 3.8,
      "work_quality": 4.1
    },
    "ratings": [
      {
        "rating_id": "uuid",
        "rating_value": 5.0,
        "review_text": "John was extremely reliable...",
        "category_ratings": {
          "punctuality": 5,
          "communication": 5,
          "work_quality": 5
        },
        "job_title": "Delivery Assistant",
        "rater": {
          "name": "Acme Logistics",
          "logo_url": "https://..."
        },
        "created_at": "2023-07-15T14:30:00Z"
      }
    ],
    "pagination": {
      "total": 26,
      "page": 1,
      "limit": 10,
      "total_pages": 3
    }
  }
}
```

### Messaging

#### GET /conversations

Get all conversations.

Query Parameters:
- status: active,archived (default: active)
- page: int
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "conversation_id": "uuid",
        "job_id": "uuid",
        "job_title": "Event Staff",
        "counterparty": {
          "user_id": "uuid",
          "name": "Bay Area Events",
          "profile_photo": "https://..."
        },
        "last_message": {
          "content": "When should I arrive exactly?",
          "sender_id": "uuid",
          "is_self": true,
          "created_at": "2023-07-27T09:45:00Z"
        },
        "unread_count": 0,
        "updated_at": "2023-07-27T09:45:00Z"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "limit": 10,
      "total_pages": 1
    }
  }
}
```

#### GET /conversations/{conversation_id}/messages

Get messages in a conversation.

Query Parameters:
- before: timestamp (for pagination)
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "conversation_id": "uuid",
    "job_id": "uuid",
    "job_title": "Event Staff",
    "counterparty": {
      "user_id": "uuid",
      "name": "Bay Area Events", 
      "profile_photo": "https://..."
    },
    "messages": [
      {
        "message_id": "uuid",
        "sender_id": "uuid",
        "is_self": false,
        "content": "Hi! Looking forward to having you at our event.",
        "message_type": "text",
        "media_url": null,
        "is_read": true,
        "created_at": "2023-07-26T14:30:00Z"
      },
      {
        "message_id": "uuid", 
        "sender_id": "uuid",
        "is_self": true,
        "content": "When should I arrive exactly?",
        "message_type": "text",
        "media_url": null,
        "is_read": true,
        "created_at": "2023-07-27T09:45:00Z"
      }
    ],
    "has_more": false
  }
}
```

#### POST /conversations/{conversation_id}/messages

Send a message.

Request:
```json
{
  "content": "Thanks for the information!",
  "message_type": "text",
  "media": null
}
```

Response:
```json
{
  "success": true,
  "data": {
    "message_id": "uuid",
    "conversation_id": "uuid",
    "created_at": "2023-07-27T10:15:00Z"
  },
  "message": "Message sent successfully"
}
```

#### POST /jobs/{job_id}/conversation

Start a conversation about a job.

Request:
```json
{
  "initial_message": "Hi, I have a question about the dress code."
}
```

Response:
```json
{
  "success": true,
  "data": {
    "conversation_id": "uuid",
    "message_id": "uuid"
  },
  "message": "Conversation started successfully"
}
```

### Notifications

#### GET /notifications

Get user notifications.

Query Parameters:
- type: all,jobs,applications,payments (default: all)
- is_read: true,false,all (default: all)
- page: int
- limit: int

Response:
```json
{
  "success": true,
  "data": {
    "unread_count": 2,
    "notifications": [
      {
        "notification_id": "uuid",
        "type": "application_accepted",
        "title": "Application Accepted",
        "content": "Your application for Event Staff at Bay Area Events has been accepted!",
        "action_type": "view_application",
        "action_url": "/applications/uuid",
        "is_read": false,
        "created_at": "2023-07-24T14:30:00Z"
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "limit": 10,
      "total_pages": 2
    }
  }
}
```

#### PUT /notifications/{notification_id}/read

Mark notification as read.

Response:
```json
{
  "success": true,
  "data": {
    "notification_id": "uuid",
    "is_read": true
  },
  "message": "Notification marked as read"
}
```

#### PUT /notifications/read-all

Mark all notifications as read.

Response:
```json
{
  "success": true,
  "message": "All notifications marked as read"
}
```

### Miscellaneous

#### GET /categories

Get job categories.

Response:
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "category_id": "uuid",
        "name": "Hospitality & Events",
        "description": "Jobs in the hospitality and events industry",
        "icon": "event_icon",
        "job_count": 42
      }
    ]
  }
}
```

#### GET /users/stats

Get user statistics.

Response for job seeker:
```json
{
  "success": true,
  "data": {
    "total_earnings": 1245.00,
    "weekly_earnings": 180.00,
    "monthly_earnings": 685.00,
    "completed_jobs": 8,
    "application_stats": {
      "pending": 2,
      "accepted": 1,
      "rejected": 1,
      "completed": 8
    },
    "average_rating": 4.0
  }
}
```

Response for employer:
```json
{
  "success": true,
  "data": {
    "active_jobs": 3,
    "expired_jobs": 2,
    "draft_jobs": 1,
    "total_applications": 24,
    "pending_applications": 12,
    "hired_workers": 8,
    "total_spent": 2480.00,
    "average_rating": 4.2
  }
}
```

## HTTP Status Codes

- 200 OK: Successful request
- 201 Created: Resource created successfully
- 400 Bad Request: Invalid request parameters
- 401 Unauthorized: Missing or invalid authentication
- 403 Forbidden: Authenticated but not authorized
- 404 Not Found: Resource not found
- 409 Conflict: Request conflicts with current state
- 422 Unprocessable Entity: Validation errors
- 500 Internal Server Error: Server error

## Error Codes

Error responses include standardized error codes:

```json
{
  "success": false,
  "data": null,
  "message": "Validation error",
  "errors": [
    {
      "code": "INVALID_PARAMETER",
      "field": "email",
      "message": "Email format is invalid"
    }
  ]
}
```

Common error codes:
- AUTH_REQUIRED: Authentication required
- INVALID_CREDENTIALS: Invalid login credentials
- RESOURCE_NOT_FOUND: Requested resource not found
- PERMISSION_DENIED: User doesn't have permission
- VALIDATION_ERROR: Input validation failed
- DUPLICATE_ENTITY: Resource already exists
- RATE_LIMIT_EXCEEDED: Too many requests 