using FluentAssertions;
using Workforce.Application.Services;
using Xunit;

namespace Workforce.Application.Tests.Services
{
    public class PasswordHashingServiceTests
    {
        private readonly PasswordHashingService _passwordHashingService;

        public PasswordHashingServiceTests()
        {
            _passwordHashingService = new PasswordHashingService();
        }

        [Fact]
        public void HashPassword_ValidPassword_ShouldReturnHashedPassword()
        {
            // Arrange
            var password = "TestPassword123!";

            // Act
            var hashedPassword = _passwordHashingService.HashPassword(password);

            // Assert
            hashedPassword.Should().NotBeNullOrEmpty();
            hashedPassword.Should().NotBe(password);
            hashedPassword.Length.Should().BeGreaterThan(50); // Hashed passwords are typically much longer
        }

        [Fact]
        public void HashPassword_SamePasswordTwice_ShouldReturnDifferentHashes()
        {
            // Arrange
            var password = "TestPassword123!";

            // Act
            var hash1 = _passwordHashingService.HashPassword(password);
            var hash2 = _passwordHashingService.HashPassword(password);

            // Assert
            hash1.Should().NotBe(hash2); // Each hash should be unique due to salt
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData(null)]
        public void HashPassword_InvalidPassword_ShouldThrowArgumentException(string? password)
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _passwordHashingService.HashPassword(password));
        }

        [Fact]
        public void VerifyPassword_CorrectPassword_ShouldReturnTrue()
        {
            // Arrange
            var password = "TestPassword123!";
            var hashedPassword = _passwordHashingService.HashPassword(password);

            // Act
            var result = _passwordHashingService.VerifyPassword(hashedPassword, password);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void VerifyPassword_IncorrectPassword_ShouldReturnFalse()
        {
            // Arrange
            var password = "TestPassword123!";
            var wrongPassword = "WrongPassword123!";
            var hashedPassword = _passwordHashingService.HashPassword(password);

            // Act
            var result = _passwordHashingService.VerifyPassword(hashedPassword, wrongPassword);

            // Assert
            result.Should().BeFalse();
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData(null)]
        public void VerifyPassword_InvalidHashedPassword_ShouldThrowArgumentException(string? hashedPassword)
        {
            // Arrange
            var password = "TestPassword123!";

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _passwordHashingService.VerifyPassword(hashedPassword, password));
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData(null)]
        public void VerifyPassword_InvalidProvidedPassword_ShouldThrowArgumentException(string? providedPassword)
        {
            // Arrange
            var password = "TestPassword123!";
            var hashedPassword = _passwordHashingService.HashPassword(password);

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _passwordHashingService.VerifyPassword(hashedPassword, providedPassword));
        }

        [Fact]
        public void VerifyPassword_MultiplePasswords_ShouldWorkCorrectly()
        {
            // Arrange
            var passwords = new[]
            {
                "Password1!",
                "AnotherPassword2@",
                "ThirdPassword3#",
                "FourthPassword4$"
            };

            var hashedPasswords = passwords.Select(p => _passwordHashingService.HashPassword(p)).ToArray();

            // Act & Assert
            for (int i = 0; i < passwords.Length; i++)
            {
                // Correct password should verify
                _passwordHashingService.VerifyPassword(hashedPasswords[i], passwords[i]).Should().BeTrue();

                // Wrong passwords should not verify
                for (int j = 0; j < passwords.Length; j++)
                {
                    if (i != j)
                    {
                        _passwordHashingService.VerifyPassword(hashedPasswords[i], passwords[j]).Should().BeFalse();
                    }
                }
            }
        }

        [Fact]
        public void HashPassword_LongPassword_ShouldWork()
        {
            // Arrange
            var longPassword = new string('a', 1000) + "Password123!";

            // Act
            var hashedPassword = _passwordHashingService.HashPassword(longPassword);
            var verificationResult = _passwordHashingService.VerifyPassword(hashedPassword, longPassword);

            // Assert
            hashedPassword.Should().NotBeNullOrEmpty();
            verificationResult.Should().BeTrue();
        }

        [Fact]
        public void HashPassword_SpecialCharacters_ShouldWork()
        {
            // Arrange
            var passwordWithSpecialChars = "Pässwörd123!@#$%^&*()_+-=[]{}|;':\",./<>?`~";

            // Act
            var hashedPassword = _passwordHashingService.HashPassword(passwordWithSpecialChars);
            var verificationResult = _passwordHashingService.VerifyPassword(hashedPassword, passwordWithSpecialChars);

            // Assert
            hashedPassword.Should().NotBeNullOrEmpty();
            verificationResult.Should().BeTrue();
        }
    }
}
