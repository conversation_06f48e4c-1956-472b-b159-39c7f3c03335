{"DatabaseSettings": {"Host": "localhost", "Port": 5432, "Database": "hirenow", "Username": "postgres", "Password": "postgres"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-security", "Issuer": "HireNow.API", "Audience": "HireNow.Client", "ExpirationMinutes": 60}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}