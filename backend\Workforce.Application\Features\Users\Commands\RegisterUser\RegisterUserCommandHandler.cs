using AutoMapper;
using MediatR;
using Workforce.Application.Services;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;
using Workforce.Domain.Repositories;
using Workforce.Shared.DTOs;

namespace Workforce.Application.Features.Users.Commands.RegisterUser
{
    /// <summary>
    /// Handler for RegisterUserCommand
    /// </summary>
    public class RegisterUserCommandHandler : IRequestHandler<RegisterUserCommand, AuthenticatedUserDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IJwtTokenService _jwtTokenService;

        public RegisterUserCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IPasswordHashingService passwordHashingService,
            IJwtTokenService jwtTokenService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _passwordHashingService = passwordHashingService;
            _jwtTokenService = jwtTokenService;
        }

        /// <summary>
        /// Handles the user registration command
        /// </summary>
        /// <param name="request">The registration command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authenticated user DTO with tokens</returns>
        public async Task<AuthenticatedUserDto> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
        {
            // Parse the role enum
            if (!Enum.TryParse<UserRole>(request.Role, out var userRole))
            {
                throw new ArgumentException($"Invalid role: {request.Role}");
            }

            // Hash the password
            var hashedPassword = _passwordHashingService.HashPassword(request.Password);

            // Create the user entity
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = request.Email.ToLowerInvariant().Trim(),
                PasswordHash = hashedPassword,
                FirstName = request.FirstName.Trim(),
                LastName = request.LastName.Trim(),
                PhoneNumber = string.IsNullOrWhiteSpace(request.PhoneNumber) ? null : request.PhoneNumber.Trim(),
                Role = userRole,
                IsVerified = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Save the user to the database
            await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Generate JWT tokens
            var accessToken = _jwtTokenService.GenerateAccessToken(user);
            var refreshToken = _jwtTokenService.GenerateRefreshToken();
            var expirationSeconds = _jwtTokenService.GetTokenExpirationSeconds();

            // Map user to DTO
            var userProfileDto = _mapper.Map<UserProfileDto>(user);

            // Create the authenticated user response
            var authenticatedUserDto = new AuthenticatedUserDto
            {
                User = userProfileDto,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                TokenType = "Bearer",
                ExpiresIn = expirationSeconds,
                ExpiresAt = DateTime.UtcNow.AddSeconds(expirationSeconds)
            };

            return authenticatedUserDto;
        }
    }
}
