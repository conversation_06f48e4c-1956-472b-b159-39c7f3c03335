using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Workforce.API.Controllers;
using Workforce.Application.Features.Employees.Commands.CreateEmployee;
using Workforce.Application.Features.Employees.Commands.DeleteEmployee;
using Workforce.Application.Features.Employees.Queries.GetEmployeeById;
using Workforce.Application.Features.Employees.Queries.GetEmployeeList;
using Workforce.Shared.DTOs;
using Xunit;

namespace Workforce.API.Tests.Controllers
{
    public class EmployeesControllerTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly EmployeesController _controller;

        public EmployeesControllerTests()
        {
            _mockMediator = new Mock<IMediator>();
            _controller = new EmployeesController(_mockMediator.Object);
        }

        [Fact]
        public async Task GetAll_ShouldReturnOkResult_WithListOfEmployees()
        {
            // Arrange
            var employees = new List<EmployeeDto>
            {
                new EmployeeDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    Position = "Software Developer"
                },
                new EmployeeDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = "Jane",
                    LastName = "Smith",
                    Email = "<EMAIL>",
                    Position = "QA Engineer"
                }
            };

            _mockMediator.Setup(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(employees);

            // Act
            var result = await _controller.GetAll();

            // Assert
            var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
            var returnedEmployees = okResult.Value.Should().BeAssignableTo<List<EmployeeDto>>().Subject;
            
            returnedEmployees.Should().HaveCount(2);
            returnedEmployees.Should().BeEquivalentTo(employees);
            
            _mockMediator.Verify(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetById_WithValidId_ShouldReturnOkResult_WithEmployee()
        {
            // Arrange
            var employeeId = Guid.NewGuid();
            var employee = new EmployeeDto
            {
                Id = employeeId,
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Position = "Software Developer"
            };

            _mockMediator.Setup(m => m.Send(It.Is<GetEmployeeByIdQuery>(q => q.Id == employeeId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(employee);

            // Act
            var result = await _controller.GetById(employeeId);

            // Assert
            var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
            var returnedEmployee = okResult.Value.Should().BeAssignableTo<EmployeeDto>().Subject;
            
            returnedEmployee.Should().NotBeNull();
            returnedEmployee.Id.Should().Be(employeeId);
            returnedEmployee.FirstName.Should().Be("John");
            returnedEmployee.LastName.Should().Be("Doe");
            
            _mockMediator.Verify(m => m.Send(It.Is<GetEmployeeByIdQuery>(q => q.Id == employeeId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetById_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var employeeId = Guid.NewGuid();

            _mockMediator.Setup(m => m.Send(It.Is<GetEmployeeByIdQuery>(q => q.Id == employeeId), It.IsAny<CancellationToken>()))
                .ReturnsAsync((EmployeeDto)null);

            // Act
            var result = await _controller.GetById(employeeId);

            // Assert
            result.Result.Should().BeOfType<NotFoundResult>();
            
            _mockMediator.Verify(m => m.Send(It.Is<GetEmployeeByIdQuery>(q => q.Id == employeeId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Create_WithValidData_ShouldReturnCreatedAtAction()
        {
            // Arrange
            var createDto = new CreateEmployeeDto
            {
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Position = "Software Developer"
            };

            var createdEmployee = new EmployeeDto
            {
                Id = Guid.NewGuid(),
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Position = "Software Developer"
            };

            _mockMediator.Setup(m => m.Send(It.IsAny<CreateEmployeeCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(createdEmployee);

            // Act
            var result = await _controller.Create(createDto);

            // Assert
            var createdAtActionResult = result.Result.Should().BeOfType<CreatedAtActionResult>().Subject;
            createdAtActionResult.ActionName.Should().Be(nameof(EmployeesController.GetById));
            createdAtActionResult.RouteValues["id"].Should().Be(createdEmployee.Id);
            
            var returnedEmployee = createdAtActionResult.Value.Should().BeAssignableTo<EmployeeDto>().Subject;
            returnedEmployee.Should().BeEquivalentTo(createdEmployee);
            
            _mockMediator.Verify(m => m.Send(It.IsAny<CreateEmployeeCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Delete_WithValidId_ShouldReturnNoContent()
        {
            // Arrange
            var employeeId = Guid.NewGuid();

            _mockMediator.Setup(m => m.Send(It.Is<DeleteEmployeeCommand>(c => c.Id == employeeId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Delete(employeeId);

            // Assert
            result.Should().BeOfType<NoContentResult>();
            
            _mockMediator.Verify(m => m.Send(It.Is<DeleteEmployeeCommand>(c => c.Id == employeeId), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Delete_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var employeeId = Guid.NewGuid();

            _mockMediator.Setup(m => m.Send(It.Is<DeleteEmployeeCommand>(c => c.Id == employeeId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.Delete(employeeId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
            
            _mockMediator.Verify(m => m.Send(It.Is<DeleteEmployeeCommand>(c => c.Id == employeeId), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
