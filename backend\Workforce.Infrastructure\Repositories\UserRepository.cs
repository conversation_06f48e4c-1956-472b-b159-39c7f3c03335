using Microsoft.EntityFrameworkCore;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;
using Workforce.Domain.Repositories;
using Workforce.Infrastructure.Data;

namespace Workforce.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for User entity operations
    /// </summary>
    public class UserRepository : Repository<User>, IUserRepository
    {
        public UserRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Gets a user by their email address
        /// </summary>
        /// <param name="email">The email address to search for</param>
        /// <returns>The user if found, null otherwise</returns>
        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower() && u.DeletedAt == null);
        }

        /// <summary>
        /// Gets a user by their phone number
        /// </summary>
        /// <param name="phoneNumber">The phone number to search for</param>
        /// <returns>The user if found, null otherwise</returns>
        public async Task<User?> GetByPhoneNumberAsync(string phoneNumber)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber && u.DeletedAt == null);
        }

        /// <summary>
        /// Checks if an email address is already in use
        /// </summary>
        /// <param name="email">The email address to check</param>
        /// <param name="excludeUserId">Optional user ID to exclude from the check (for updates)</param>
        /// <returns>True if email exists, false otherwise</returns>
        public async Task<bool> EmailExistsAsync(string email, Guid? excludeUserId = null)
        {
            var query = _context.Set<User>()
                .Where(u => u.Email.ToLower() == email.ToLower() && u.DeletedAt == null);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.UserId != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Checks if a phone number is already in use
        /// </summary>
        /// <param name="phoneNumber">The phone number to check</param>
        /// <param name="excludeUserId">Optional user ID to exclude from the check (for updates)</param>
        /// <returns>True if phone number exists, false otherwise</returns>
        public async Task<bool> PhoneNumberExistsAsync(string phoneNumber, Guid? excludeUserId = null)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var query = _context.Set<User>()
                .Where(u => u.PhoneNumber == phoneNumber && u.DeletedAt == null);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.UserId != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Gets users by their role
        /// </summary>
        /// <param name="role">The user role to filter by</param>
        /// <returns>Collection of users with the specified role</returns>
        public async Task<IEnumerable<User>> GetByRoleAsync(UserRole role)
        {
            return await _context.Set<User>()
                .Where(u => u.Role == role && u.DeletedAt == null)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// Gets active users (not soft deleted)
        /// </summary>
        /// <returns>Collection of active users</returns>
        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await _context.Set<User>()
                .Where(u => u.IsActive && u.DeletedAt == null)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// Updates the user's last login timestamp
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="loginTime">The login timestamp</param>
        public async Task UpdateLastLoginAsync(Guid userId, DateTime loginTime)
        {
            var user = await GetByIdAsync(userId);
            if (user != null)
            {
                user.LastLogin = loginTime;
                user.UpdatedAt = DateTime.UtcNow;
                await UpdateAsync(user);
            }
        }

        /// <summary>
        /// Updates the user's last seen online timestamp
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="lastSeenTime">The last seen timestamp</param>
        public async Task UpdateLastSeenOnlineAsync(Guid userId, DateTime lastSeenTime)
        {
            var user = await GetByIdAsync(userId);
            if (user != null)
            {
                user.LastSeenOnline = lastSeenTime;
                user.UpdatedAt = DateTime.UtcNow;
                await UpdateAsync(user);
            }
        }
    }
}
