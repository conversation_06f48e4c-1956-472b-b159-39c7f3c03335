using MediatR;
using Workforce.Shared.DTOs;

namespace Workforce.Application.Features.Users.Commands.RegisterUser
{
    /// <summary>
    /// Command for registering a new user
    /// </summary>
    public class RegisterUserCommand : IRequest<AuthenticatedUserDto>
    {
        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's password
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Password confirmation
        /// </summary>
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number (optional)
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User's role (<PERSON><PERSON>eeker or Employer)
        /// </summary>
        public string Role { get; set; } = string.Empty;
    }
}
