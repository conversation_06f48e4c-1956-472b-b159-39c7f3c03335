using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Workforce.Application.Features.Users.Commands.RegisterUser;
using Workforce.Shared.DTOs;

namespace Workforce.API.Controllers
{
    /// <summary>
    /// Controller for authentication operations
    /// </summary>
    [ApiController]
    [Route("api/v1/[controller]")]
    [Produces("application/json")]
    public class AuthController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IMapper _mapper;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IMediator mediator, IMapper mapper, ILogger<AuthController> logger)
        {
            _mediator = mediator;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Registers a new user account
        /// </summary>
        /// <param name="registerUserDto">User registration data</param>
        /// <returns>Authenticated user with tokens</returns>
        /// <response code="201">User successfully registered</response>
        /// <response code="400">Invalid input data or validation errors</response>
        /// <response code="409">Email or phone number already exists</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("register")]
        [ProducesResponseType(typeof(AuthenticatedUserDto), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AuthenticatedUserDto>> Register([FromBody] RegisterUserDto registerUserDto)
        {
            try
            {
                _logger.LogInformation("User registration attempt for email: {Email}", registerUserDto.Email);

                // Map DTO to command
                var command = _mapper.Map<RegisterUserCommand>(registerUserDto);

                // Execute the command
                var result = await _mediator.Send(command);

                _logger.LogInformation("User successfully registered with ID: {UserId}", result.User.UserId);

                return CreatedAtAction(
                    nameof(Register),
                    new { id = result.User.UserId },
                    result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid argument during registration: {Message}", ex.Message);
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Input",
                    Detail = ex.Message,
                    Status = StatusCodes.Status400BadRequest
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Invalid operation during registration: {Message}", ex.Message);
                return Conflict(new ProblemDetails
                {
                    Title = "Registration Conflict",
                    Detail = ex.Message,
                    Status = StatusCodes.Status409Conflict
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during user registration for email: {Email}", registerUserDto.Email);
                return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                {
                    Title = "Internal Server Error",
                    Detail = "An unexpected error occurred while processing your request.",
                    Status = StatusCodes.Status500InternalServerError
                });
            }
        }

        /// <summary>
        /// Health check endpoint for the authentication service
        /// </summary>
        /// <returns>Service health status</returns>
        /// <response code="200">Service is healthy</response>
        [HttpGet("health")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        public ActionResult<object> Health()
        {
            return Ok(new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Service = "Authentication"
            });
        }
    }
}
