// Include any global setup for the Jest testing environment
const { jest } = require('@jest/globals');
require('@testing-library/jest-native/extend-expect');

// Mock the Expo modules that might cause issues in the testing environment
jest.mock('expo-font', () => ({}));
jest.mock('expo-asset', () => ({}));

// Mock the Animated module instead of NativeAnimatedHelper
jest.mock('react-native/Libraries/Animated/Animated', () => ({
  timing: () => ({
    start: jest.fn(),
  }),
  loop: jest.fn(),
  Value: jest.fn(() => ({
    interpolate: jest.fn(),
  })),
}));

// This enables proper error messages for React tests
globalThis.ErrorUtils = {
  setGlobalHandler: jest.fn(),
};
