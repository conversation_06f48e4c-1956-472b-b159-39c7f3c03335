name: Backend CI/CD

on:
  push:
    branches: [ main ]
    paths:
      - 'backend/**'
      - '.github/workflows/backend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'backend/**'
      - '.github/workflows/backend.yml'

env:
  DOTNET_INSTALL_DIR: ${{ github.workspace }}/dotnet
jobs:
  setup:
    name: Setup Environment
    runs-on: [self-hosted]
    steps:
      - uses: actions/checkout@v4
      
      - name: Install .NET SDK
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

  build-and-test:
    name: Build and Test
    needs: setup
    runs-on: [self-hosted]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'
          
      - name: Restore dependencies
        working-directory: backend
        run: dotnet restore
        
      - name: Build
        working-directory: backend
        run: dotnet build
        
      - name: Test
        working-directory: backend
        run: dotnet test

  deploy:
    name: Deploy
    needs: build-and-test
    if: github.ref == 'refs/heads/main'
    runs-on: [self-hosted]
    services:
      docker:
        image: docker:20.10.16
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'
          
      # - name: Login to Docker Hub
      #   uses: docker/login-action@v3
      #   with:
      #     username: ${{ secrets.DOCKERHUB_USERNAME }}
      #     password: ${{ secrets.DOCKERHUB_TOKEN }}
          
      - name: Build and Push Docker Image
        working-directory: backend
        run: |
          docker build -t workforce-api:${{ github.run_number }} .
          # docker push workforce-api:${{ github.run_number }}

      # Add your backend deployment steps here
      # For example, deploying to Kubernetes or other cloud platforms
      - name: Deploy Backend
        run: |
          echo "Add your backend deployment steps here"
          # Example: Deploy to Kubernetes
          # - name: Deploy to Kubernetes
          #   uses: azure/k8s-deploy@v1
          #   with:
          #     manifests: |
          #       k8s/deployment.yaml
          #       k8s/service.yaml
          #     images: |
          #       your-org/backend:${{ github.sha }} 