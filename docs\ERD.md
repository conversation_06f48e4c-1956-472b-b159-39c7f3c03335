# HireNow Database Schema (ERD)

```mermaid
erDiagram
    USERS {
        uuid user_id PK
        string email UK
        string password_hash
        string phone_number UK
        string first_name
        string last_name
        string role "job_seeker/employer"
        string profile_photo_url
        boolean is_verified
        boolean is_active
        string device_token
        string fcm_token
        jsonb preferences
        jsonb app_settings
        timestamp last_login
        timestamp last_seen_online
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    JOB_SEEKER_PROFILES {
        uuid profile_id PK
        uuid user_id FK
        string full_name
        date birth_date
        text bio
        string profile_photo
        string address
        point location_coordinates
        int max_travel_distance
        jsonb skills
        jsonb education "array of education objects"
        jsonb certifications "array of certification objects"
        jsonb experience "array of experience objects"
        jsonb availability
        jsonb preferred_job_types
        decimal preferred_pay_rate
        decimal avg_rating
        decimal response_rate
        int response_time
        int completed_jobs
        int cancelled_jobs
        decimal total_earnings
        boolean verified_identity
        string background_check_status
        boolean is_featured
        boolean offline_data_access
        timestamp created_at
        timestamp updated_at
    }
    
    JOB_SEEKER_AVAILABILITY {
        uuid availability_id PK
        uuid job_seeker_id FK
        int day_of_week
        time start_time
        time end_time
        boolean recurring
        date specific_date
        timestamp created_at
        timestamp updated_at
    }
    
    EMPLOYER_PROFILES {
        uuid profile_id PK
        uuid user_id FK
        string company_name
        string company_logo
        string industry
        string company_size
        string website_url
        string business_phone
        string address
        point location_coordinates
        int geo_fence_radius
        text company_description
        boolean is_verified_business
        string business_license_number
        string tax_id
        decimal avg_rating
        decimal response_rate
        int response_time
        int jobs_posted
        int active_jobs
        decimal hire_rate
        boolean subscription_active
        string subscription_tier
        timestamp created_at
        timestamp updated_at
    }
    
    SUBSCRIPTIONS {
        uuid subscription_id PK
        uuid user_id FK
        string plan_type
        decimal amount
        string currency
        string billing_frequency
        string status
        jsonb features
        int job_slots_limit
        int featured_listings_limit
        timestamp start_date
        timestamp end_date
        string payment_method
        boolean auto_renew
        string external_subscription_id
        string receipt_url
        timestamp created_at
        timestamp updated_at
    }
    
    JOB_CATEGORIES {
        uuid category_id PK
        string name UK
        string description
        string icon
        string mobile_icon
        string color_hex
        int job_count
        boolean is_active
        int display_order
        timestamp created_at
        timestamp updated_at
    }
    
    JOB_LISTINGS {
        uuid job_id PK
        uuid employer_id FK
        uuid category_id FK
        string title
        text description
        string status "draft/active/filled/expired/cancelled/completed"
        point location
        string address
        string location_name
        decimal pay_rate
        string payment_type "hourly/fixed"
        int duration_hours
        timestamp start_datetime
        timestamp end_datetime
        int positions_available
        int positions_filled
        jsonb required_skills
        boolean requires_experience
        string experience_level
        string education_level
        text physical_requirements
        text dress_code
        text additional_instructions
        boolean featured_listing
        string urgency_level
        timestamp expiry_date
        jsonb application_questions
        timestamp application_deadline
        jsonb benefits
        jsonb photos
        int view_count
        int unique_view_count
        int application_count
        boolean allow_remote_checkin
        string checkin_code
        boolean is_promoted
        int promotion_radius
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    JOB_VIEWS {
        uuid view_id PK
        uuid job_id FK
        uuid user_id FK
        timestamp viewed_at
        string source
        string session_id
    }
    
    APPLICATIONS {
        uuid application_id PK
        uuid job_id FK
        uuid job_seeker_id FK
        string status "submitted/under_review/accepted/rejected/scheduled/in_progress/completed/paid/rated"
        text cover_note
        jsonb answers "responses to application_questions"
        jsonb attachments
        boolean employer_viewed
        timestamp employer_viewed_at
        string rejection_reason
        timestamp scheduled_time
        timestamp check_in_time
        point check_in_location
        timestamp check_out_time
        point check_out_location
        decimal hours_worked
        decimal final_pay
        boolean is_favorite
        string cancellation_reason
        uuid cancelled_by FK
        boolean offline_action_required
        timestamp applied_at
        timestamp updated_at
    }
    
    SAVED_JOBS {
        uuid saved_id PK
        uuid job_id FK
        uuid user_id FK
        text notes
        timestamp reminder_datetime
        boolean reminder_sent
        timestamp saved_at
    }
    
    PAYMENTS {
        uuid payment_id PK
        uuid job_id FK
        uuid application_id FK
        uuid job_seeker_id FK
        uuid employer_id FK
        decimal amount
        decimal tip_amount
        decimal platform_fee
        decimal tax_amount
        decimal total_amount
        string currency
        string status "pending/processing/completed/failed/refunded"
        string payment_method
        string payment_provider
        string transaction_id
        string invoice_number
        string invoice_url
        string receipt_url
        text notes
        timestamp due_date
        timestamp paid_at
        timestamp created_at
        timestamp updated_at
    }
    
    PAYMENT_METHODS {
        uuid payment_method_id PK
        uuid user_id FK
        string type "card/bank_account/paypal"
        string provider
        string account_last4
        string account_holder_name
        date expiry_date
        jsonb billing_address
        boolean is_default
        string status
        string external_payment_method_id
        timestamp created_at
        timestamp updated_at
    }
    
    TRANSACTIONS {
        uuid transaction_id PK
        uuid user_id FK
        uuid payment_id FK
        string type "payment/refund/adjustment/fee/withdrawal"
        decimal amount
        string currency
        text description
        string status "pending/completed/failed" 
        string external_transaction_id
        timestamp transaction_date
        timestamp created_at
    }
    
    EARNINGS_SUMMARIES {
        uuid summary_id PK
        uuid user_id FK
        date period_start
        date period_end
        decimal total_earnings
        decimal total_fees
        decimal net_amount
        int job_count
        decimal hours_worked
        decimal avg_hourly_rate
        boolean is_paid
        timestamp payment_date
        timestamp created_at
        timestamp updated_at
    }
    
    RATINGS {
        uuid rating_id PK
        uuid rater_id FK
        uuid ratee_id FK
        uuid job_id FK
        uuid application_id FK
        decimal rating_value
        text review_text
        jsonb category_ratings "punctuality, communication, etc."
        boolean is_public
        boolean is_flagged
        boolean has_responded
        text response_text
        timestamp created_at
        timestamp updated_at
    }
    
    NOTIFICATIONS {
        uuid notification_id PK
        uuid user_id FK
        string type
        string title
        string content
        string action_type
        string action_url
        string deep_link
        boolean is_read
        boolean is_push_sent
        boolean is_email_sent
        jsonb related_ids
        timestamp read_at
        timestamp push_sent_at
        timestamp email_sent_at
        timestamp created_at
        timestamp expires_at
    }
    
    MESSAGES {
        uuid message_id PK
        uuid conversation_id FK
        uuid sender_id FK
        uuid recipient_id FK
        text content
        string message_type "text/image/document/location"
        string media_url
        jsonb location_data
        boolean is_read
        string delivery_status
        timestamp read_at
        timestamp created_at
        string offline_id
        boolean is_system_message
    }
    
    CONVERSATIONS {
        uuid conversation_id PK
        uuid job_id FK
        uuid employer_id FK
        uuid job_seeker_id FK
        string status "active/archived/blocked"
        timestamp last_message_at
        int unread_count_employer
        int unread_count_job_seeker
        boolean is_pinned_employer
        boolean is_pinned_job_seeker
        timestamp created_at
        timestamp updated_at
    }
    
    REPORTS {
        uuid report_id PK
        uuid reporter_id FK
        uuid reported_id FK
        string entity_type "user/job/review/message"
        uuid entity_id
        string reason
        text description
        string status "pending/reviewed/resolved/dismissed"
        text admin_notes
        string action_taken
        timestamp created_at
        timestamp updated_at
    }
    
    SAVED_LOCATIONS {
        uuid location_id PK
        uuid user_id FK
        string name
        string address
        point location_coordinates
        boolean is_home
        boolean is_work
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    CHECK_IN_HISTORY {
        uuid check_id PK
        uuid application_id FK
        uuid user_id FK
        uuid job_id FK
        string check_type "in/out"
        timestamp timestamp
        point location_coordinates
        string address
        string verification_method "gps/code/manager"
        string verification_code
        jsonb device_info
        boolean is_valid
        text notes
    }
    
    SYSTEM_SETTINGS {
        string setting_key PK
        jsonb setting_value
        string description
        timestamp updated_at
    }
    
    AUDIT_LOGS {
        uuid log_id PK
        uuid user_id FK
        string action
        string entity_type
        uuid entity_id
        jsonb previous_state
        jsonb new_state
        string ip_address
        string user_agent
        jsonb device_info
        timestamp created_at
    }
    
    APP_VERSIONS {
        uuid version_id PK
        string platform "ios/android"
        string version_number
        string build_number
        boolean is_critical_update
        jsonb features
        string min_required_version
        timestamp release_date
        timestamp created_at
    }
    
    OFFLINE_SYNC_QUEUE {
        uuid sync_id PK
        uuid user_id FK
        string entity_type
        string entity_id
        string action "create/update/delete"
        jsonb data
        string status "pending/synced/failed"
        text error_message
        int retry_count
        timestamp created_at
        timestamp synced_at
    }

    USERS ||--o| JOB_SEEKER_PROFILES : has
    USERS ||--o| EMPLOYER_PROFILES : has
    USERS ||--o{ SUBSCRIPTIONS : purchases
    USERS ||--o{ SAVED_JOBS : saves
    USERS ||--o{ NOTIFICATIONS : receives
    USERS ||--o{ PAYMENT_METHODS : registers
    USERS ||--o{ SAVED_LOCATIONS : saves
    USERS ||--o{ TRANSACTIONS : has
    USERS ||--o{ EARNINGS_SUMMARIES : has
    
    JOB_SEEKER_PROFILES ||--o{ JOB_SEEKER_AVAILABILITY : sets
    
    EMPLOYER_PROFILES ||--o{ JOB_LISTINGS : creates
    JOB_CATEGORIES ||--o{ JOB_LISTINGS : categorizes
    
    JOB_LISTINGS ||--o{ APPLICATIONS : receives
    JOB_LISTINGS ||--o{ SAVED_JOBS : saved_as
    JOB_LISTINGS ||--o{ JOB_VIEWS : viewed_by
    
    JOB_SEEKER_PROFILES ||--o{ APPLICATIONS : submits
    
    APPLICATIONS ||--o| PAYMENTS : generates
    APPLICATIONS ||--o{ RATINGS : receives
    APPLICATIONS ||--o{ CHECK_IN_HISTORY : records
    
    USERS ||--o{ RATINGS : gives
    USERS ||--o{ RATINGS : receives
    
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ MESSAGES : receives
    
    CONVERSATIONS ||--o{ MESSAGES : contains
    JOB_LISTINGS ||--o| CONVERSATIONS : relates_to
    
    USERS ||--o{ REPORTS : files
    USERS ||--o{ REPORTS : subject_of
    
    USERS ||--o{ AUDIT_LOGS : generates
    USERS ||--o{ OFFLINE_SYNC_QUEUE : creates
    
    PAYMENTS ||--o{ TRANSACTIONS : creates
```

## Table Descriptions

### Core User Data

#### USERS
Central table storing authentication and basic account information for all users, with role differentiation and mobile-specific fields like FCM tokens.

#### JOB_SEEKER_PROFILES
Extended profile information specific to job seekers, including detailed personal information, skills, education, work history, performance metrics, and preferences.

#### JOB_SEEKER_AVAILABILITY
Detailed weekly and specific date availability for job seekers, enabling precise scheduling of work.

#### EMPLOYER_PROFILES
Detailed company information and metrics for employers posting jobs, including business verification status and response metrics.

#### SUBSCRIPTIONS
Tracks premium subscription plans, features, and billing information for users.

### Job Management

#### JOB_CATEGORIES
Standard categories for organizing job listings with metadata like icons, mobile-specific icons, and color schemes.

#### JOB_LISTINGS
Comprehensive data for job postings, including detailed requirements, compensation, location, status, check-in options, and application metrics.

#### JOB_VIEWS
Tracks user views of job listings for analytics and recommendations to improve job discovery.

#### APPLICATIONS
Tracks the complete lifecycle of job applications from submission through completion and payment, including check-in/out data.

#### SAVED_JOBS
Records jobs that users have bookmarked for later reference, with optional notes and reminders.

### Financial System

#### PAYMENTS
Detailed records of all financial transactions, including fee breakdowns, payment status, and downloadable receipts.

#### PAYMENT_METHODS
Securely stores payment instrument information for both receiving and making payments.

#### TRANSACTIONS
Comprehensive record of all financial transactions for reporting and history, linked to payments.

#### EARNINGS_SUMMARIES
Periodic earnings summaries for reporting and dashboards, aggregating financial information.

### Reputation & Quality Control

#### RATINGS
Bidirectional rating and review system with granular category ratings and response options for reviewed users.

#### REPORTS
System for users to report problematic content or behavior for administrative review.

### Communication

#### MESSAGES
Individual messages exchanged between users with support for various media types, including location sharing.

#### CONVERSATIONS
Organizes messages into conversations related to specific jobs or applications, with read tracking.

#### NOTIFICATIONS
System and user-generated notifications for real-time updates with support for multiple delivery channels.

### Location Services

#### SAVED_LOCATIONS
User-saved locations for easier job searching and applications, such as home and work addresses.

#### CHECK_IN_HISTORY
Records of user check-ins and check-outs for job attendance tracking, with verification options.

### System Tables

#### SYSTEM_SETTINGS
Global configuration settings for the application stored as key-value pairs.

#### AUDIT_LOGS
Detailed tracking of all significant data changes for security and compliance.

#### APP_VERSIONS
Tracks mobile app versions for update management and feature availability.

#### OFFLINE_SYNC_QUEUE
Queue for syncing actions performed while offline to ensure data consistency.

## Key Relationships

- Each USER has one specific role (job seeker or employer) and one corresponding profile
- JOB_SEEKER_PROFILES have multiple JOB_SEEKER_AVAILABILITY records to define working hours
- JOB_LISTINGS belong to specific JOB_CATEGORIES and are created by EMPLOYER_PROFILES
- APPLICATIONS connect JOB_SEEKERS with JOB_LISTINGS and track the entire workflow
- PAYMENTS are linked to specific APPLICATIONS, tracking financial transactions
- RATINGS are given between USERS and linked to specific APPLICATIONS/JOBS
- CONVERSATIONS organize MESSAGES between JOB_SEEKERS and EMPLOYERS
- NOTIFICATIONS alert USERS about important events across the platform
- CHECK_IN_HISTORY tracks attendance at job locations with verification options
- OFFLINE_SYNC_QUEUE ensures data consistency for mobile users with intermittent connectivity

## Important Design Considerations

1. **Mobile-First Design**: Includes features for offline functionality, push notifications, and optimized UX components.
2. **Location Services**: Comprehensive geographical data support for job matching, check-ins, and saved locations.
3. **Offline Operation**: Queue system for offline operations and syncing when connectivity is restored.
4. **Soft Deletion**: Many tables include `deleted_at` timestamps to implement soft deletion.
5. **Status Tracking**: Status fields use enumerated types to track entity state throughout lifecycles.
6. **Flexible Storage**: JSON fields store structured data that may vary in format or evolve over time.
7. **Timestamps**: Comprehensive timestamp tracking for created/updated/deleted actions.
8. **Audit Trail**: Complete history of significant changes for security and support.
9. **Performance Optimization**: Strategic denormalization and indexing for performance.
10. **Financial Tracking**: Detailed financial records for earnings, payments, and transactions.

## Database Implementation Notes

- UUID primary keys ensure security and facilitate distributed systems
- Spatial data types and indexes for location-based operations
- Soft deletion pattern implemented via `deleted_at` timestamps
- Appropriate constraints (NOT NULL, UNIQUE) should be applied based on business rules
- JSON/JSONB fields provide flexibility for evolving data structures
- Foreign key constraints maintain referential integrity
- Timestamp fields capture complete audit history
- Enum types constrain status and type fields to valid values
- Indexes on frequently queried fields optimize performance 