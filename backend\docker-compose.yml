version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: workforce-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password123}
      - POSTGRES_DB=${POSTGRES_DB:-workforce}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - workforce-network

  # PgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4
    container_name: workforce-pgadmin
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-password123}
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - workforce-network

  # ASP.NET Core Web API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: workforce-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNET_ENVIRONMENT:-Development}
      - ASPNETCORE_HTTP_PORTS=8080
      - DatabaseSettings__Host=${DB_HOST:-postgres}
      - DatabaseSettings__Port=${DB_PORT:-5432}
      - DatabaseSettings__Database=${DB_NAME:-workforce}
      - DatabaseSettings__Username=${DB_USER:-postgres}
      - DatabaseSettings__Password=${DB_PASSWORD:-password123}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - workforce-network

volumes:
  postgres-data:
    name: workforce-postgres-data

networks:
  workforce-network:
    name: workforce-network
    driver: bridge 