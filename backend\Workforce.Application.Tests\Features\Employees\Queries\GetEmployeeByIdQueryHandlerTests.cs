using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FluentAssertions;
using Moq;
using Workforce.Application.Features.Employees.Queries.GetEmployeeById;
using Workforce.Application.Mappings;
using Workforce.Domain.Entities;
using Workforce.Domain.Repositories;
using Workforce.Shared.DTOs;
using Xunit;

namespace Workforce.Application.Tests.Features.Employees.Queries
{
    public class GetEmployeeByIdQueryHandlerTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IEmployeeRepository> _mockEmployeeRepository;
        private readonly IMapper _mapper;

        public GetEmployeeByIdQueryHandlerTests()
        {
            // Setup repositories
            _mockEmployeeRepository = new Mock<IEmployeeRepository>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUnitOfWork.Setup(uow => uow.Employees).Returns(_mockEmployeeRepository.Object);
            
            // Setup AutoMapper
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MappingProfile>();
            });
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public async Task Handle_WithValidId_ShouldReturnEmployeeDto()
        {
            // Arrange
            var employeeId = Guid.NewGuid();
            var departmentId = Guid.NewGuid();
            
            var employee = new Employee
            {
                Id = employeeId,
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                PhoneNumber = "************",
                HireDate = new DateTime(2023, 1, 15),
                Position = "Software Developer",
                Salary = 75000m,
                DepartmentId = departmentId,
                Department = new Department { Id = departmentId, Name = "IT" }
            };
            
            var query = new GetEmployeeByIdQuery { Id = employeeId };
            
            _mockEmployeeRepository.Setup(repo => repo.GetByIdAsync(employeeId))
                .ReturnsAsync(employee);
                
            var handler = new GetEmployeeByIdQueryHandler(_mockUnitOfWork.Object, _mapper);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeOfType<EmployeeDto>();
            result.Id.Should().Be(employeeId);
            result.FirstName.Should().Be("John");
            result.LastName.Should().Be("Doe");
            result.Email.Should().Be("<EMAIL>");
            result.DepartmentId.Should().Be(departmentId);
            result.DepartmentName.Should().Be("IT");
            
            _mockEmployeeRepository.Verify(repo => repo.GetByIdAsync(employeeId), Times.Once);
        }

        [Fact]
        public async Task Handle_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var employeeId = Guid.NewGuid();
            var query = new GetEmployeeByIdQuery { Id = employeeId };
            
            _mockEmployeeRepository.Setup(repo => repo.GetByIdAsync(employeeId))
                .ReturnsAsync((Employee)null);
                
            var handler = new GetEmployeeByIdQueryHandler(_mockUnitOfWork.Object, _mapper);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().BeNull();
            _mockEmployeeRepository.Verify(repo => repo.GetByIdAsync(employeeId), Times.Once);
        }
    }
}
