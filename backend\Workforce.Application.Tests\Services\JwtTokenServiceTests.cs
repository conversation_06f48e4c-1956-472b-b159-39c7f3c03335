using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Workforce.Application.Services;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;
using Xunit;

namespace Workforce.Application.Tests.Services
{
    public class JwtTokenServiceTests
    {
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly JwtTokenService _jwtTokenService;
        private readonly string _secretKey = "this-is-a-very-long-secret-key-for-testing-purposes-that-is-at-least-32-characters";
        private readonly string _issuer = "TestIssuer";
        private readonly string _audience = "TestAudience";
        private readonly int _expirationMinutes = 60;

        public JwtTokenServiceTests()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            
            _mockConfiguration.Setup(x => x["JwtSettings:SecretKey"]).Returns(_secretKey);
            _mockConfiguration.Setup(x => x["JwtSettings:Issuer"]).Returns(_issuer);
            _mockConfiguration.Setup(x => x["JwtSettings:Audience"]).Returns(_audience);
            _mockConfiguration.Setup(x => x["JwtSettings:ExpirationMinutes"]).Returns(_expirationMinutes.ToString());

            _jwtTokenService = new JwtTokenService(_mockConfiguration.Object, Mock.Of<ILogger<JwtTokenService>>());
        }

        [Fact]
        public void Constructor_MissingSecretKey_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var mockConfig = new Mock<IConfiguration>();
            mockConfig.Setup(x => x["JwtSettings:SecretKey"]).Returns((string)null!);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => new JwtTokenService(mockConfig.Object, Mock.Of<ILogger<JwtTokenService>>()));
        }

        [Fact]
        public void Constructor_MissingIssuer_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var mockConfig = new Mock<IConfiguration>();
            mockConfig.Setup(x => x["JwtSettings:SecretKey"]).Returns(_secretKey);
            mockConfig.Setup(x => x["JwtSettings:Issuer"]).Returns((string)null!);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => new JwtTokenService(mockConfig.Object, Mock.Of<ILogger<JwtTokenService>>()));
        }

        [Fact]
        public void GenerateAccessToken_ValidUser_ShouldReturnValidJwtToken()
        {
            // Arrange
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+1234567890",
                Role = UserRole.JobSeeker,
                IsVerified = true,
                IsActive = true
            };

            // Act
            var token = _jwtTokenService.GenerateAccessToken(user);

            // Assert
            token.Should().NotBeNullOrEmpty();

            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);

            jsonToken.Should().NotBeNull();
            jsonToken.Claims.Should().Contain(c => c.Type == "nameid" && c.Value == user.UserId.ToString());
            jsonToken.Claims.Should().Contain(c => c.Type == "email" && c.Value == user.Email);
            jsonToken.Claims.Should().Contain(c => c.Type == "unique_name" && c.Value == user.FullName);
            jsonToken.Claims.Should().Contain(c => c.Type == "role" && c.Value == user.Role.ToString());
            jsonToken.Claims.Should().Contain(c => c.Type == "first_name" && c.Value == user.FirstName);
            jsonToken.Claims.Should().Contain(c => c.Type == "last_name" && c.Value == user.LastName);
            jsonToken.Claims.Should().Contain(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone" && c.Value == user.PhoneNumber);
        }

        [Fact]
        public void GenerateAccessToken_UserWithEmptyPhoneNumber_ShouldNotIncludePhoneClaim()
        {
            // Arrange
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "",
                Role = UserRole.JobSeeker,
                IsVerified = true,
                IsActive = true
            };

            // Act
            var token = _jwtTokenService.GenerateAccessToken(user);

            // Assert
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);

            jsonToken.Claims.Should().NotContain(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone");
        }

        [Fact]
        public void GenerateRefreshToken_ShouldReturnBase64String()
        {
            // Act
            var refreshToken = _jwtTokenService.GenerateRefreshToken();

            // Assert
            refreshToken.Should().NotBeNullOrEmpty();
            refreshToken.Length.Should().BeGreaterThan(40); // Base64 encoded 32 bytes should be longer than 40 chars
            
            // Should be valid base64
            var bytes = Convert.FromBase64String(refreshToken);
            bytes.Length.Should().Be(32);
        }

        [Fact]
        public void GenerateRefreshToken_MultipleCalls_ShouldReturnDifferentTokens()
        {
            // Act
            var token1 = _jwtTokenService.GenerateRefreshToken();
            var token2 = _jwtTokenService.GenerateRefreshToken();

            // Assert
            token1.Should().NotBe(token2);
        }

        [Fact]
        public void GetTokenExpirationSeconds_ShouldReturnCorrectValue()
        {
            // Act
            var expirationSeconds = _jwtTokenService.GetTokenExpirationSeconds();

            // Assert
            expirationSeconds.Should().Be(_expirationMinutes * 60);
        }

        [Fact]
        public void ValidateToken_ValidToken_ShouldReturnTrue()
        {
            // Arrange
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+1234567890",
                Role = UserRole.JobSeeker,
                IsVerified = true,
                IsActive = true
            };

            var token = _jwtTokenService.GenerateAccessToken(user);

            // Act
            var isValid = _jwtTokenService.ValidateToken(token);

            // Assert
            isValid.Should().BeTrue();
        }

        [Theory]
        [InlineData("")]
        [InlineData("invalid.token")]
        [InlineData("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid")]
        public void ValidateToken_InvalidToken_ShouldReturnFalse(string invalidToken)
        {
            // Act
            var isValid = _jwtTokenService.ValidateToken(invalidToken);

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void GetUserIdFromToken_ValidToken_ShouldReturnUserId()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var user = new User
            {
                UserId = userId,
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+1234567890",
                Role = UserRole.JobSeeker,
                IsVerified = true,
                IsActive = true
            };

            var token = _jwtTokenService.GenerateAccessToken(user);

            // Act
            var extractedUserId = _jwtTokenService.GetUserIdFromToken(token);

            // Assert
            extractedUserId.Should().Be(userId);
        }

        [Theory]
        [InlineData("")]
        [InlineData("invalid.token")]
        [InlineData("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid")]
        public void GetUserIdFromToken_InvalidToken_ShouldReturnNull(string invalidToken)
        {
            // Act
            var userId = _jwtTokenService.GetUserIdFromToken(invalidToken);

            // Assert
            userId.Should().BeNull();
        }

        [Theory]
        [InlineData(UserRole.JobSeeker)]
        [InlineData(UserRole.Employer)]
        public void GenerateAccessToken_DifferentRoles_ShouldIncludeCorrectRoleClaim(UserRole role)
        {
            // Arrange
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+1234567890",
                Role = role,
                IsVerified = true,
                IsActive = true
            };

            // Act
            var token = _jwtTokenService.GenerateAccessToken(user);

            // Assert
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);

            jsonToken.Claims.Should().Contain(c => c.Type == "role" && c.Value == role.ToString());
        }
    }
}
