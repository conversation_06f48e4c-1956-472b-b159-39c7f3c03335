<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Workforce.Application\Workforce.Application.csproj" />
    <ProjectReference Include="..\Workforce.Infrastructure\Workforce.Infrastructure.csproj" />
    <ProjectReference Include="..\Workforce.Shared\Workforce.Shared.csproj" />
  </ItemGroup>

</Project>
