// Mock for expo-router
import React from 'react';
import { Text, Pressable } from 'react-native';

// Basic Link component mock using functional component style
export const Link = ({ children, onPress, ...props }) => (
  <Pressable onPress={onPress} {...props}>
    <Text>{children}</Text>
  </Pressable>
);

// Common router hooks
export const router = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
};

export const useRouter = () => ({
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
});

export const useLocalSearchParams = jest.fn();
export const useGlobalSearchParams = jest.fn();
export const useSegments = jest.fn();

// Export named exports
module.exports = {
  Link,
  router,
  useRouter,
  useLocalSearchParams,
  useGlobalSearchParams,
  useSegments,
};
