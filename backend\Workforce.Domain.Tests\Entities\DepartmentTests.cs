using System;
using System.Collections.Generic;
using FluentAssertions;
using Workforce.Domain.Entities;
using Xunit;

namespace Workforce.Domain.Tests.Entities
{
    public class DepartmentTests
    {
        [Fact]
        public void Department_ShouldHaveCorrectProperties()
        {
            // Arrange
            var id = Guid.NewGuid();
            var employees = new List<Employee>
            {
                new Employee { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe" },
                new Employee { Id = Guid.NewGuid(), FirstName = "Jane", LastName = "Smith" }
            };
            
            // Act
            var department = new Department
            {
                Id = id,
                Name = "Engineering",
                Description = "Software Engineering Department",
                Employees = employees
            };
            
            // Assert
            department.Id.Should().Be(id);
            department.Name.Should().Be("Engineering");
            department.Description.Should().Be("Software Engineering Department");
            department.Employees.Should().BeEquivalentTo(employees);
            department.Employees.Should().HaveCount(2);
        }
    }
}
