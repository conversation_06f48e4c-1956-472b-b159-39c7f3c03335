# Workforce App API

A .NET 8 Web API project built with Clean Architecture, CQRS pattern, and Entity Framework Core for PostgreSQL. The CI/CD pipeline uses direct path-based detection in each job for improved clarity and simplicity.

## Project Structure

This solution follows the Clean Architecture principles and is organized into the following projects:

### Workforce.Domain

Contains enterprise business rules and entities. This layer is independent of any external dependencies.

- **Entities**: Core business objects
- **Repositories**: Interfaces defining data access operations
- **Exceptions**: Domain-specific exceptions

### Workforce.Application

Contains application business rules and implements the CQRS pattern using MediatR.

- **Features**: Organized by entity and operation type
  - **Commands**: Write operations (Create, Update, Delete)
  - **Queries**: Read operations (GetById, GetList)
- **Mappings**: AutoMapper profiles for object mapping
- **Validators**: FluentValidation rules

### Workforce.Infrastructure

Implements the interfaces defined in the Domain layer and contains external dependencies.

- **Data**: EF Core DbContext and configurations
- **Repositories**: Implementation of repository interfaces
- **Migrations**: Database schema migrations

### Workforce.API

The entry point of the application, contains controllers and configuration.

- **Controllers**: API endpoints
- **Startup.cs**: Application configuration and middleware pipeline
- **Program.cs**: Application entry point
- **Configuration**: Application settings

### Workforce.Shared

Contains DTOs (Data Transfer Objects) shared between the API and clients.

- **DTOs**: Data transfer objects for API requests and responses

## Getting Started

### Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [PostgreSQL](https://www.postgresql.org/download/)

### Database Configuration

Update the connection string in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=workforce;Username=postgres;Password=yourpassword"
  }
}
```

### Common .NET CLI Commands

#### Build the Solution

```bash
dotnet build
```

#### Run the API

```bash
cd Workforce.API
dotnet run
```

Or from the solution root:

```bash
dotnet run --project Workforce.API
```

#### Watch Mode (Auto-restart on file changes)

```bash
cd Workforce.API
dotnet watch run
```

#### Entity Framework Migrations

Create a new migration:

```bash
cd Workforce.Infrastructure
dotnet ef migrations add MigrationName --startup-project ../Workforce.API
```

Apply migrations to the database:

```bash
cd Workforce.Infrastructure
dotnet ef database update --startup-project ../Workforce.API
```

Generate SQL script for migrations:

```bash
cd Workforce.Infrastructure
dotnet ef migrations script --startup-project ../Workforce.API
```

#### Testing

Run all tests:

```bash
dotnet test
```

Run specific test project:

```bash
dotnet test Workforce.Tests
```

#### Publish the API

```bash
dotnet publish Workforce.API -c Release -o ./publish
```

## Architecture Overview

### Traditional Startup.cs Pattern

This project uses the traditional ASP.NET Core pattern with separate `Program.cs` and `Startup.cs` files:

- **Program.cs**: Contains the application entry point and host configuration
- **Startup.cs**: Contains service registration (ConfigureServices) and middleware configuration (Configure)

This approach provides a clear separation between application bootstrapping and configuration.

### Clean Architecture

This project follows Clean Architecture principles, which means:

- **Independence of Frameworks**: The business logic doesn't depend on external frameworks
- **Testability**: Business rules can be tested without UI, database, or external elements
- **Independence of UI**: The UI can change without changing the business rules
- **Independence of Database**: The database can be swapped out without affecting business rules

### CQRS Pattern

The Command Query Responsibility Segregation pattern separates read and write operations:

- **Commands**: Change the state of the system (Create, Update, Delete)
- **Queries**: Return data without modifying state (Get, List)

This separation allows for optimizing each path independently.

### Entity Framework Core

The project uses Entity Framework Core as the ORM (Object-Relational Mapper) with PostgreSQL.

- **Code-First Approach**: Define models in code and generate the database schema
- **Migrations**: Track and apply database schema changes
- **Repository Pattern**: Abstract data access behind repository interfaces

## Environment Variables

The application uses environment variables for configuration, especially for sensitive information like database credentials. This approach improves security by avoiding hardcoded values.

### Setting Up Environment Variables

1. Create a `.env` file in the root directory (same level as `docker-compose.yml`):
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your actual values:
   ```
   # Database Configuration
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=your_secure_password
   POSTGRES_DB=workforce
   
   # Other configuration values...
   ```

3. Docker Compose will automatically load variables from this file when you run:
   ```bash
   docker compose up -d
   ```

**Important Notes:**
- The `.env` file contains sensitive information and should NEVER be committed to version control
- The `.env.example` file is provided as a template and should not contain real credentials
- In production environments, consider using a secrets management system
